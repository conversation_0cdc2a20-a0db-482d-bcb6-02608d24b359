const { LoanWorkflow, WORKFLOW_STATUS, WORKFLOW_ACTIONS } = require('../models/loanWorkflow.model');
const LoanFormSchema = require('../models/loanFormSchema.model');
const LoanFormData = require('../models/loanFormData.model');
const { PrimaryApplication } = require('../schema/primaryApplication.model');
const pdfGeneratorService = require('../services/pdfGenerator.service');
const { generateFrontendResponse } = require('../utils/frontendHelper');

/**
 * Create a complete loan application with form and workflow
 */
const createLoanApplication = async (req, res) => {
  try {
    const { loanType, formData, isDraft = true } = req.body;
    const userId = req.user.userId;
    const branchId = req.user.branchId;
    
    // Validate user role
    if (req.user.role !== 'deputy_manager') {
      return res.status(403).json({
        success: false,
        message: 'Only deputy managers can create loan applications'
      });
    }
    
    // Get form schema
    const schema = await LoanFormSchema.getActiveSchemaByLoanType(loanType);
    if (!schema) {
      return res.status(404).json({
        success: false,
        message: `Form schema not found for loan type: ${loanType}`
      });
    }
    
    // Extract loan amount for workflow
    const loanAmount = formData.loanAmount || formData.loan_amount;
    if (!loanAmount) {
      return res.status(400).json({
        success: false,
        message: 'Loan amount is required'
      });
    }
    
    // Create workflow first
    const workflow = new LoanWorkflow({
      loanAmount: Number(loanAmount),
      createdBy: userId,
      branch: branchId,
      currentStatus: WORKFLOW_STATUS.DRAFT,
      workflowHistory: [{
        action: WORKFLOW_ACTIONS.SAVE_DRAFT,
        fromStatus: null,
        toStatus: WORKFLOW_STATUS.DRAFT,
        performedBy: userId,
        performedByRole: req.user.role,
        loanAmount: Number(loanAmount),
        comments: 'Loan application created'
      }]
    });
    
    await workflow.save();
    
    // Create form data linked to workflow
    const loanFormData = new LoanFormData({
      formSchema: schema._id,
      workflowId: workflow._id,
      loanType,
      formData,
      submittedBy: userId,
      branch: branchId,
      status: isDraft ? 'draft' : 'submitted',
      submissionMetadata: {
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
        formVersion: schema.version
      }
    });
    
    // Add to workflow
    workflow.applicationData = loanFormData._id;
    
    // Validate and process if not draft
    if (!isDraft) {
      const validationResults = await loanFormData.validateFormData();
      
      if (!validationResults.isValid) {
        // Delete workflow if validation fails
        await LoanWorkflow.findByIdAndDelete(workflow._id);
        
        return res.status(400).json({
          success: false,
          message: 'Form validation failed',
          errors: validationResults.errors,
          warnings: validationResults.warnings
        });
      }
      
      // Map to primary application format
      await loanFormData.mapToPrimaryApplication();
      
      // Create primary application record for compatibility
      const primaryApp = new PrimaryApplication({
        applicationId: workflow.applicationId,
        personalInfo: loanFormData.mappedData.personalInfo || {},
        phone: loanFormData.mappedData.phone || {},
        alternatePhone: loanFormData.mappedData.alternatePhone || {},
        loanInfo: loanFormData.mappedData.loanInfo || {},
        occupation: loanFormData.mappedData.occupation || {},
        financialInfo: loanFormData.mappedData.financialInfo || {},
        applicantDetails: [],
        businessAddress: loanFormData.mappedData.businessAddress || {},
        homeAddress: loanFormData.mappedData.homeAddress || {},
        createdBy: userId,
        status: 1 // DRAFT status
      });
      
      await primaryApp.save();
    }
    
    await loanFormData.addChangeHistory(
      'created',
      userId,
      { formData: formData },
      isDraft ? 'Application created as draft' : 'Application created and submitted'
    );
    
    await workflow.save();
    await loanFormData.save();
    
    // Generate frontend response
    const frontendData = generateFrontendResponse(workflow, req.user.role, userId, false);
    
    res.status(201).json({
      success: true,
      message: isDraft ? 'Loan application created as draft' : 'Loan application created and submitted',
      data: {
        workflowId: workflow._id,
        applicationId: workflow.applicationId,
        formDataId: loanFormData._id,
        loanType,
        loanAmount,
        status: workflow.currentStatus,
        formStatus: loanFormData.status,
        validationResults: loanFormData.validationResults,
        ui: frontendData.ui,
        metadata: frontendData.metadata
      }
    });
    
  } catch (error) {
    console.error('Error creating loan application:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create loan application',
      error: error.message
    });
  }
};

/**
 * Update existing loan application
 */
const updateLoanApplication = async (req, res) => {
  try {
    const { workflowId } = req.params;
    const { formData, isDraft = true } = req.body;
    const userId = req.user.userId;
    
    // Get workflow and form data
    const workflow = await LoanWorkflow.findById(workflowId);
    if (!workflow) {
      return res.status(404).json({
        success: false,
        message: 'Loan application not found'
      });
    }
    
    // Check permissions
    if (req.user.role === 'deputy_manager' && workflow.createdBy.toString() !== userId.toString()) {
      return res.status(403).json({
        success: false,
        message: 'You can only update your own applications'
      });
    }
    
    // Check if application can be edited
    if (![WORKFLOW_STATUS.DRAFT, WORKFLOW_STATUS.RETURNED_FOR_CHANGES].includes(workflow.currentStatus)) {
      return res.status(400).json({
        success: false,
        message: 'Application cannot be edited in current status'
      });
    }
    
    const loanFormData = await LoanFormData.getByWorkflowId(workflowId);
    if (!loanFormData) {
      return res.status(404).json({
        success: false,
        message: 'Form data not found'
      });
    }
    
    // Update form data
    loanFormData.formData = { ...loanFormData.formData, ...formData };
    loanFormData.status = isDraft ? 'draft' : 'submitted';
    loanFormData.version += 1;
    
    // Update loan amount in workflow if changed
    if (formData.loanAmount && formData.loanAmount !== workflow.loanAmount) {
      workflow.loanAmount = Number(formData.loanAmount);
    }
    
    // Validate if not draft
    if (!isDraft) {
      const validationResults = await loanFormData.validateFormData();
      
      if (!validationResults.isValid) {
        return res.status(400).json({
          success: false,
          message: 'Form validation failed',
          errors: validationResults.errors
        });
      }
      
      await loanFormData.mapToPrimaryApplication();
    }
    
    await loanFormData.addChangeHistory(
      'updated',
      userId,
      { formData: formData },
      isDraft ? 'Application updated as draft' : 'Application updated and submitted'
    );
    
    await workflow.save();
    await loanFormData.save();
    
    // Generate frontend response
    const frontendData = generateFrontendResponse(workflow, req.user.role, userId, false);
    
    res.json({
      success: true,
      message: isDraft ? 'Application updated as draft' : 'Application updated and submitted',
      data: {
        workflowId: workflow._id,
        formDataId: loanFormData._id,
        version: loanFormData.version,
        status: workflow.currentStatus,
        formStatus: loanFormData.status,
        validationResults: loanFormData.validationResults,
        ui: frontendData.ui
      }
    });
    
  } catch (error) {
    console.error('Error updating loan application:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update loan application',
      error: error.message
    });
  }
};

/**
 * Get complete loan application details
 */
const getLoanApplication = async (req, res) => {
  try {
    const { workflowId } = req.params;
    const userId = req.user.userId;
    const userRole = req.user.role;
    
    // Get workflow with populated data
    const workflow = await LoanWorkflow.findById(workflowId)
      .populate('currentReviewer', 'firstName lastName email role')
      .populate('createdBy', 'firstName lastName email')
      .populate('branch', 'name code');
    
    if (!workflow) {
      return res.status(404).json({
        success: false,
        message: 'Loan application not found'
      });
    }
    
    // Check access permissions
    let hasAccess = false;
    switch (userRole) {
      case 'super_admin':
      case 'general_manager':
        hasAccess = true;
        break;
      case 'deputy_manager':
        hasAccess = workflow.createdBy._id.toString() === userId.toString();
        break;
      case 'branch_manager':
        hasAccess = workflow.branch._id.toString() === req.user.branchId?.toString();
        break;
    }
    
    if (!hasAccess) {
      return res.status(403).json({
        success: false,
        message: 'Access denied to this application'
      });
    }
    
    // Get form data
    const formData = await LoanFormData.getByWorkflowId(workflowId);
    
    // Generate frontend response
    const frontendData = generateFrontendResponse(workflow, userRole, userId, true);
    
    res.json({
      success: true,
      data: {
        ...frontendData,
        formData: formData ? {
          formDataId: formData._id,
          loanType: formData.loanType,
          formData: formData.formData,
          mappedData: formData.mappedData,
          status: formData.status,
          version: formData.version,
          validationResults: formData.validationResults,
          pdfStatus: formData.pdfStatus,
          attachments: formData.attachments,
          changeHistory: formData.changeHistory
        } : null
      }
    });
    
  } catch (error) {
    console.error('Error getting loan application:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get loan application',
      error: error.message
    });
  }
};

/**
 * Submit application to branch manager with PDF generation
 */
const submitApplicationToBM = async (req, res) => {
  try {
    const { workflowId } = req.params;
    const { comments, generatePDF = true } = req.body;
    const userId = req.user.userId;
    
    // Get form data
    const formData = await LoanFormData.getByWorkflowId(workflowId);
    if (!formData) {
      return res.status(404).json({
        success: false,
        message: 'Form data not found'
      });
    }
    
    // Validate form data before submission
    const validationResults = await formData.validateFormData();
    if (!validationResults.isValid) {
      return res.status(400).json({
        success: false,
        message: 'Cannot submit application with validation errors',
        errors: validationResults.errors
      });
    }
    
    // Generate PDF if requested
    if (generatePDF && !formData.pdfStatus.isGenerated) {
      try {
        await pdfGeneratorService.generatePDF(formData._id);
      } catch (pdfError) {
        console.warn('PDF generation failed, but continuing with submission:', pdfError);
      }
    }
    
    // Use existing workflow submission logic
    const workflowController = require('./loanWorkflow.controller');
    req.params.workflowId = workflowId;
    req.body.comments = comments;
    
    await workflowController.submitToBranchManager(req, res);
    
  } catch (error) {
    console.error('Error submitting application:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to submit application',
      error: error.message
    });
  }
};

module.exports = {
  createLoanApplication,
  updateLoanApplication,
  getLoanApplication,
  submitApplicationToBM
};
