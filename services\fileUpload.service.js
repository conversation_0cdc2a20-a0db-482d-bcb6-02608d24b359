const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const { <PERSON><PERSON>r<PERSON>and<PERSON> } = require('../utils/common');

const writeFile = promisify(fs.writeFile);
const unlink = promisify(fs.unlink);
const mkdir = promisify(fs.mkdir);
const exists = promisify(fs.exists);

// Ensure upload directory exists
const ensureUploadsDir = async () => {
    const uploadDir = process.env.UPLOAD_DIR || 'uploads';
    if (!(await exists(uploadDir))) {
        await mkdir(uploadDir, { recursive: true });
    }
    return uploadDir;
};

/**
 * Upload file to local storage
 * @param {Object} file - Multer file object
 * @param {string} folder - Subfolder in uploads directory
 * @returns {Promise<Object>} - Upload result with file path and URL
 */
const uploadFile = async (file, folder = 'profile-pictures') => {
    try {
        // If no file is provided, return default avatar
        if (!file) {
            return {
                path: 'default-avatar.png',
                url: '/uploads/default-avatar.png'
            };
        }

        const uploadDir = await ensureUploadsDir();
        const fileExt = path.extname(file.originalname).toLowerCase();
        const fileName = `${Date.now()}-${Math.round(Math.random() * 1E9)}${fileExt}`;
        const filePath = path.join(folder, fileName);
        const fullPath = path.join(uploadDir, filePath);

        // Ensure the subdirectory exists
        const dirName = path.dirname(fullPath);
        if (!(await exists(dirName))) {
            await mkdir(dirName, { recursive: true });
        }

        // Move the file from temp location to uploads folder
        await writeFile(fullPath, file.buffer);

        // Clean up the temporary file
        if (file.path && (await exists(file.path))) {
            await unlink(file.path);
        }

        return {
            path: filePath,
            url: `/uploads/${filePath}`
        };
    } catch (error) {
        // Clean up the temporary file in case of error
        if (file && file.path && (await exists(file.path))) {
            await unlink(file.path);
        }
        throw new ErrorHandler(`File upload failed: ${error.message}`, 500);
    }
};

/**
 * Delete file from local storage
 * @param {string} filePath - Path to the file to delete
 * @returns {Promise<boolean>} - True if deletion was successful
 */
const deleteFile = async (filePath) => {
    try {
        if (!filePath || filePath === 'default-avatar.png') {
            return true; // Skip deletion for default avatars
        }

        const fullPath = path.join(process.env.UPLOAD_DIR || 'uploads', filePath);
        
        if (await exists(fullPath)) {
            await unlink(fullPath);
        }
        
        return true;
    } catch (error) {
        console.error('Error deleting file:', error);
        return false;
    }
};

module.exports = {
    uploadFile,
    deleteFile
};
