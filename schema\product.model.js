const mongoose = require("mongoose");
const Schema = mongoose.Schema;

const productSchema = new Schema(
  {
    loanTypeId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "LoanType", // This should match the name of your LoanType model
      required: true,
    },
    productCode: { type: String, required: true, unique: true },
    minAmount: { type: String, required: true },
    maxAmount: { type: String, required: true },
    timePeriod: { type: String, required: true },
    interestRate: { type: String, required: true },
    isJoint: { type: Boolean, required: true },
    isDeleted: {
      type: Boolean,
      default: false,
    },
    productStatus: {
      type: String,
      required: true,
      enum: {
        values: ["active", "inactive"],
        message: "Status must be active or inactive",
      },
      default: "active",
    },
  },
  { timestamps: true }
);

// Add index for frequently searched fields
productSchema.index({ product_status: 1 });

module.exports = mongoose.model("Product", productSchema);
