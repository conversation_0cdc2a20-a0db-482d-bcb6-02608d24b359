const mongoose = require("mongoose");
const Schema = mongoose.Schema;

// Phone Number Schema
const PhoneSchema = new Schema({
  code: { type: String, required: true, default: "+91" },
  number: { type: String, required: true },
});

// Address Schema (reusable for both business and home addresses)
const AddressSchema = new Schema({
  line1: { type: String, required: true },
  line2: { type: String, required: true },
  line3: { type: String, required: true },
  city: { type: mongoose.Schema.Types.ObjectId, ref: "City", required: true },
  state: { type: mongoose.Schema.Types.ObjectId, ref: "State", required: true },
  pincode: { type: String, required: true },
  country: { type: String, required: true },
});

// Personal Information Schema

const PersonalInfoSchema = new Schema(
  {
    fname: { type: String, required: true },
    mname: { type: String, required: false },
    lname: { type: String, required: true },
    email: { type: String, required: true },
    gender: { type: String, required: true },
    motherName: { type: String, required: true },
    dateOfBirth: { type: String, required: true },
    maritalStatus: { type: String, required: true },
  },
  {
    timestamps: true, // optional: adds createdAt and updatedAt
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  }
);

// Virtual field for full name
PersonalInfoSchema.virtual("fullName").get(function () {
  return [this.fname, this.mname, this.lname]
    .filter(Boolean) // Removes undefined, null, or empty strings
    .join(" ");
});

// Application code: FNAME_dd/mm/yyyy
PersonalInfoSchema.virtual("applicationCode").get(function () {
  const namePart = this.fname
    ? this.fname.replace(/\s+/g, "").toUpperCase()
    : "";

  if (!this.dateOfBirth) return namePart;

  const dateObj = new Date(this.dateOfBirth);
  const day = String(dateObj.getUTCDate()).padStart(2, "0");
  const month = String(dateObj.getUTCMonth() + 1).padStart(2, "0");
  const year = dateObj.getUTCFullYear();

  const formattedDOB = `${day}-${month}-${year}`;

  return `${namePart}_${formattedDOB}`;
});
// Loan Information Schema
const LoanInfoSchema = new Schema({
  loanProductId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Product",
    required: true,
  },
  applicationType: { type: String, required: true },
  purposeOfLoan: { type: String, required: true },
});

// Occupation Schema
const OccupationSchema = new Schema({
  type: { type: String, required: false },
  typeName: { type: String, required: false },
  constitution: { type: String, required: false },
  dateOfBusinessStart: { type: String, required: false },
  experience: { type: String, required: false },
});

// Financial Information Schema
const FinancialInfoSchema = new Schema({
  financialYear: { type: String, required: true },
  totalRepaymentCapacity: { type: String, required: true },
  grandTotalIncome: { type: String, required: true },
  roiRate: { type: String, required: true },
  firstBorrowerFinancialIncome: { type: String, required: true },
  secondBorrowerFinancialIncome: { type: String, required: false },
  thirdBorrowerFinancialIncome: { type: String, required: false },
  firstBorrowerName: { type: String, required: true },
  secondBorrowerName: { type: String, required: false },
  thirdBorrowerName: { type: String, required: false },
});

// Main Application Schema
// Define application statuses
const APPLICATION_STATUS = {
  DRAFT: 1,
  EMPLOYEE_REVIEW: 2,
  MANAGER_REVIEW: 3,
  CEO_APPROVAL: 4,
  APPROVED: 5,
  REJECTED: 0
};

// Define approval actions
const APPROVAL_ACTION = {
  SUBMIT: 'submit',
  APPROVE: 'approve',
  REJECT: 'reject',
  REQUEST_CHANGES: 'request_changes',
  BYPASS: 'bypass'
};

// Approval History Schema
const ApprovalHistorySchema = new Schema({
  action: { 
    type: String, 
    enum: Object.values(APPROVAL_ACTION),
    required: true 
  },
  performedBy: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'User',
    required: true 
  },
  role: { type: String, required: true },
  comments: { type: String },
  status: { 
    type: Number, 
    enum: Object.values(APPLICATION_STATUS),
    required: true 
  },
  timestamp: { type: Date, default: Date.now }
});

// Main Application Schema
const primaryApplication = new Schema({
  applicationId: { 
    type: String, 
    unique: true,
    required: true 
  },
  personalInfo: { type: PersonalInfoSchema, required: true },
  phone: { type: PhoneSchema, required: true },
  alternatePhone: { type: PhoneSchema, required: true },
  loanInfo: { type: LoanInfoSchema, required: true },
  occupation: { type: OccupationSchema, required: true },
  financialInfo: { type: FinancialInfoSchema, required: true },
  applicantDetails: { type: Array, required: true },
  businessAddress: { type: AddressSchema, required: true },
  homeAddress: { type: AddressSchema, required: true },
  status: { 
    type: Number, 
    enum: Object.values(APPLICATION_STATUS),
    default: APPLICATION_STATUS.DRAFT,
    required: true 
  },
  currentReviewer: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'User',
    required: false 
  },
  createdBy: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'User',
    required: true 
  },
  approvalHistory: [ApprovalHistorySchema],
  isActive: { type: Boolean, default: true },
  rejectionReason: { type: String }
}, {
  timestamps: true
});

// Generate application ID before saving
primaryApplication.pre('save', async function(next) {
  if (this.isNew) {
    const count = await this.constructor.countDocuments();
    this.applicationId = `APP-${(count + 1).toString().padStart(6, '0')}`;
  }
  next();
});

// Add method to update application status with approval history
primaryApplication.methods.updateStatus = async function({
  action,
  userId,
  role,
  comments = '',
  status
}) {
  const historyEntry = {
    action,
    performedBy: userId,
    role,
    comments,
    status
  };

  this.approvalHistory.push(historyEntry);
  this.status = status;
  
  // Clear current reviewer when status changes
  this.currentReviewer = undefined;
  
  return this.save();
};

// Add static method to get next status
primaryApplication.statics.getNextStatus = function(currentStatus, isCeo = false) {
  if (isCeo) return APPLICATION_STATUS.APPROVED;
  
  switch(currentStatus) {
    case APPLICATION_STATUS.DRAFT:
      return APPLICATION_STATUS.EMPLOYEE_REVIEW;
    case APPLICATION_STATUS.EMPLOYEE_REVIEW:
      return APPLICATION_STATUS.MANAGER_REVIEW;
    case APPLICATION_STATUS.MANAGER_REVIEW:
      return APPLICATION_STATUS.CEO_APPROVAL;
    case APPLICATION_STATUS.CEO_APPROVAL:
      return APPLICATION_STATUS.APPROVED;
    default:
      return currentStatus;
  }
};

// Add virtuals
primaryApplication.virtual('statusText').get(function() {
  return Object.keys(APPLICATION_STATUS).find(
    key => APPLICATION_STATUS[key] === this.status
  );
});

// Apply the virtual to the schema
primaryApplication.set('toJSON', { virtuals: true });
primaryApplication.set('toObject', { virtuals: true });

// Export the model with constants
primaryApplication.statics.STATUS = APPLICATION_STATUS;
primaryApplication.statics.ACTIONS = APPROVAL_ACTION;

const PrimaryApplication = mongoose.model("PrimaryApplication", primaryApplication);

// Export the model with constants
module.exports = {
  PrimaryApplication,
  APPLICATION_STATUS,
  APPROVAL_ACTION
};
