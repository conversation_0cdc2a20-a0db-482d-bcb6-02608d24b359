const mongoose = require("mongoose");
const bcrypt = require("bcryptjs");
const { ROLES } = require("../constants/roles");

const userSchema = new mongoose.Schema({
  // Authentication
  email: {
    type: String,
    required: [true, "Email is required"],
    unique: true,
    trim: true,
    lowercase: true,
    match: [
      /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
      "Please provide a valid email",
    ],
  },
  password: {
    type: String,
    required: [true, "Password is required"],
    minlength: [8, "Password must be at least 8 characters long"],
    select: false,
  },

  // Role and Access
  role: {
    type: String,
    enum: Object.values(ROLES),
    default: ROLES.DEPUTY_MANAGER,
    required: true,
  },
  branch: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Branch",
    required: function () {
      return [ROLES.BRANCH_MANAGER, ROLES.DEPUTY_MANAGER].includes(this.role);
    },
  },
  isActive: {
    type: Boolean,
    default: true,
  },
  lastLogin: {
    type: Date,
    default: null,
  },

  // Personal Information
  username: {
    type: String,
    required: [true, "Username is required"],
    unique: true,
    trim: true,
    minlength: [3, "Username must be at least 3 characters long"],
  },
  firstName: {
    type: String,
    required: [true, "First name is required"],
    trim: true,
    maxlength: [50, "First name cannot be longer than 50 characters"],
  },
  middleName: {
    type: String,
    trim: true,
  },
  lastName: {
    type: String,
    required: [true, "Last name is required"],
    trim: true,
    maxlength: [50, "Last name cannot be longer than 50 characters"],
  },
  dateOfBirth: {
    type: Date,
    required: [true, "Date of birth is required"],
  },
  gender: {
    type: String,
    enum: ["male", "female", "other", "prefer_not_to_say"],
    required: true,
  },
  phoneNumber: {
    type: String,
    required: [true, "Phone number is required"],
    match: [/^[0-9]{10,15}$/, "Please provide a valid phone number"],
  },

  // Address Information
  address: {
    street: {
      type: String,
      required: [true, "Street address is required"],
    },
    city: {
      type: String,
      required: [true, "City is required"],
    },
    state: {
      type: String,
      required: [true, "State is required"],
    },
    postalCode: {
      type: String,
      required: [true, "Postal code is required"],
    },
    country: {
      type: String,
      required: [true, "Country is required"],
      default: "India",
    },
  },

  // Employment Information
  employeeId: {
    type: String,
    unique: true,
    sparse: true,
  },
  branch: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Branch",
    required: [true, "Branch is required"],
  },
  department: {
    type: String,
    required: [true, "Department is required"],
  },
  designation: {
    type: String,
    required: [true, "Designation is required"],
  },
  dateOfJoining: {
    type: Date,
    required: [true, "Date of joining is required"],
  },

  // Profile Settings
  profilePicture: {
    type: String,
    default: "default.jpg",
  },
  preferredLanguage: {
    type: String,
    default: "en",
    enum: ["en", "hi", "mr", "gu", "ta", "te", "kn", "ml", "bn", "pa"],
  },

  // System Fields
  createdAt: {
    type: Date,
    default: Date.now,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
  },
});

// Hash password before saving
userSchema.pre("save", async function (next) {
  if (!this.isModified("password")) return next();

  try {
    const salt = await bcrypt.genSalt(10);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Method to compare password
userSchema.methods.comparePassword = async function (candidatePassword) {
  try {
    return await bcrypt.compare(candidatePassword, this.password);
  } catch (error) {
    throw error;
  }
};

// Virtual for full name
userSchema.virtual("fullName").get(function () {
  return `${this.firstName} ${this.lastName}`.trim();
});

// Virtual for formatted address
userSchema.virtual("formattedAddress").get(function () {
  return `${this.address.street}, ${this.address.city}, ${this.address.state} ${this.address.postalCode}, ${this.address.country}`;
});

// Method to check if user has specific role
userSchema.methods.hasRole = function (role) {
  // Handle backward compatibility with 'superadmin' role
  if (role === "superadmin" && this.role === "super_admin") {
    return true;
  }
  return this.role === role;
};

// Method to get user's permissions
userSchema.methods.getPermissions = function () {
  const rolePermissions = {
    super_admin: ["*"],
    general_manager: ["*"],
    branch_manager: [
      "applications:review",
      "applications:approve",
      "users:view:branch",
    ],
    deputy_manager: [
      "applications:create",
      "applications:view:own",
      "profile:view",
      "profile:edit",
    ],
  };

  return rolePermissions[this.role] || [];
};

// Method to check if user has permission
userSchema.methods.hasPermission = function (permission) {
  // Super admin always has all permissions
  if (this.role === "super_admin") {
    return true;
  }
  const permissions = this.getPermissions();
  return permissions.includes("*") || permissions.includes(permission);
};

// Method to update last login timestamp
userSchema.methods.updateLastLogin = async function () {
  this.lastLogin = new Date();
  await this.save({ validateBeforeSave: false });
};

// Update updatedAt field before saving
userSchema.pre("save", function (next) {
  this.updatedAt = new Date();
  next();
});

const User = mongoose.model("User", userSchema);

module.exports = User;
