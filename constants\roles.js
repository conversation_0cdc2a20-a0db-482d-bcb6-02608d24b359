module.exports = {
  ROLES: {
    SUPER_ADMIN: 'super_admin',
    GENERAL_MANAGER: 'general_manager',
    BRANCH_MANAGER: 'branch_manager',
    DEPUTY_MANAGER: 'deputy_manager'
  },
  
  // Role hierarchy (from highest to lowest)
  ROLE_HIERARCHY: [
    'super_admin',
    'general_manager',
    'branch_manager',
    'deputy_manager'
  ],
  
  // Application status workflow
  STATUS: {
    DRAFT: 'draft',
    PENDING_APPROVAL: 'pending_approval',
    APPROVED: 'approved',
    REJECTED: 'rejected',
    DISBURSED: 'disbursed'
  },
  
  // Actions that can be performed on applications
  ACTIONS: {
    SUBMIT: 'submit',
    APPROVE: 'approve',
    REJECT: 'reject',
    RETURN: 'return',
    DISBURSE: 'disburse'
  },
  
  // Permissions for each role
  PERMISSIONS: {
    deputy_manager: {
      canCreate: true,
      canEdit: ['draft', 'returned'],
      canView: ['draft', 'pending_approval', 'approved', 'rejected', 'disbursed'],
      canSubmit: true,
      canApprove: false,
      canReject: false,
      canReturn: false,
      canDisburse: false
    },
    branch_manager: {
      canCreate: true,
      canEdit: ['draft', 'returned'],
      canView: ['draft', 'pending_approval', 'approved', 'rejected', 'disbursed'],
      canSubmit: true,
      canApprove: true,
      canReject: true,
      canReturn: true,
      canDisburse: false
    },
    general_manager: {
      canCreate: true,
      canEdit: ['draft', 'pending_approval', 'approved', 'returned', 'rejected', 'disbursed'],
      canView: ['draft', 'pending_approval', 'approved', 'rejected', 'disbursed', 'returned'],
      canSubmit: true,
      canApprove: true,
      canReject: true,
      canReturn: true,
      canDisburse: true,
      canBypass: true
    },
    super_admin: {
      canCreate: true,
      canEdit: ['draft', 'pending_approval', 'approved', 'returned', 'rejected', 'disbursed'],
      canView: ['draft', 'pending_approval', 'approved', 'rejected', 'disbursed', 'returned'],
      canSubmit: true,
      canApprove: true,
      canReject: true,
      canReturn: true,
      canDisburse: true,
      canBypass: true
    }
  }
};
