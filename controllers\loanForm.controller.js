const LoanFormSchema = require('../models/loanFormSchema.model');
const LoanFormData = require('../models/loanFormData.model');
const { LoanWorkflow } = require('../models/loanWorkflow.model');
const { generateFrontendResponse } = require('../utils/frontendHelper');

/**
 * Get form schema for a specific loan type
 */
const getFormSchema = async (req, res) => {
  try {
    const { loanType } = req.params;
    
    const schema = await LoanFormSchema.getActiveSchemaByLoanType(loanType);
    
    if (!schema) {
      return res.status(404).json({
        success: false,
        message: `Form schema not found for loan type: ${loanType}`
      });
    }
    
    // Organize data for frontend consumption
    const sectionsWithFields = schema.getSectionsOrdered().map(section => ({
      ...section.toObject(),
      fields: schema.getFieldsBySection(section.sectionName)
    }));
    
    res.json({
      success: true,
      data: {
        loanType: schema.loanType,
        loanTypeName: schema.loanTypeName,
        version: schema.version,
        description: schema.description,
        sections: sectionsWithFields,
        pdfTemplate: schema.pdfTemplate,
        workflowConfig: schema.workflowConfig
      }
    });
    
  } catch (error) {
    console.error('Error getting form schema:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get form schema',
      error: error.message
    });
  }
};

/**
 * Get all available loan form schemas
 */
const getAllFormSchemas = async (req, res) => {
  try {
    const schemas = await LoanFormSchema.getAllActiveSchemas();
    
    const schemaList = schemas.map(schema => ({
      loanType: schema.loanType,
      loanTypeName: schema.loanTypeName,
      version: schema.version,
      description: schema.description,
      sectionsCount: schema.sections.length,
      fieldsCount: schema.fields.length
    }));
    
    res.json({
      success: true,
      data: {
        schemas: schemaList,
        total: schemaList.length
      }
    });
    
  } catch (error) {
    console.error('Error getting form schemas:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get form schemas',
      error: error.message
    });
  }
};

/**
 * Create or update form data
 */
const saveFormData = async (req, res) => {
  try {
    const { loanType, formData, workflowId, isDraft = true } = req.body;
    const userId = req.user.userId;
    const branchId = req.user.branchId;
    
    // Get form schema
    const schema = await LoanFormSchema.getActiveSchemaByLoanType(loanType);
    if (!schema) {
      return res.status(404).json({
        success: false,
        message: `Form schema not found for loan type: ${loanType}`
      });
    }
    
    // Check if form data already exists for this workflow
    let existingFormData = null;
    if (workflowId) {
      existingFormData = await LoanFormData.getByWorkflowId(workflowId);
    }
    
    let loanFormData;
    
    if (existingFormData) {
      // Update existing form data
      existingFormData.formData = { ...existingFormData.formData, ...formData };
      existingFormData.status = isDraft ? 'draft' : 'submitted';
      existingFormData.version += 1;
      
      await existingFormData.addChangeHistory(
        'updated',
        userId,
        { formData: formData },
        isDraft ? 'Form data saved as draft' : 'Form data submitted'
      );
      
      loanFormData = existingFormData;
    } else {
      // Create new form data
      loanFormData = new LoanFormData({
        formSchema: schema._id,
        workflowId,
        loanType,
        formData,
        submittedBy: userId,
        branch: branchId,
        status: isDraft ? 'draft' : 'submitted',
        submissionMetadata: {
          ipAddress: req.ip,
          userAgent: req.get('User-Agent'),
          formVersion: schema.version
        }
      });
      
      await loanFormData.addChangeHistory(
        'created',
        userId,
        { formData: formData },
        isDraft ? 'Form data created as draft' : 'Form data created and submitted'
      );
    }
    
    // Validate form data if not draft
    if (!isDraft) {
      const validationResults = await loanFormData.validateFormData();
      
      if (!validationResults.isValid) {
        return res.status(400).json({
          success: false,
          message: 'Form validation failed',
          errors: validationResults.errors,
          warnings: validationResults.warnings
        });
      }
      
      // Map to primary application format
      await loanFormData.mapToPrimaryApplication();
    }
    
    await loanFormData.save();
    
    res.json({
      success: true,
      message: isDraft ? 'Form data saved as draft' : 'Form data submitted successfully',
      data: {
        formDataId: loanFormData._id,
        workflowId: loanFormData.workflowId,
        status: loanFormData.status,
        version: loanFormData.version,
        validationResults: loanFormData.validationResults
      }
    });
    
  } catch (error) {
    console.error('Error saving form data:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to save form data',
      error: error.message
    });
  }
};

/**
 * Get form data by workflow ID
 */
const getFormData = async (req, res) => {
  try {
    const { workflowId } = req.params;
    
    const formData = await LoanFormData.getByWorkflowId(workflowId);
    
    if (!formData) {
      return res.status(404).json({
        success: false,
        message: 'Form data not found'
      });
    }
    
    // Check access permissions
    const userRole = req.user.role;
    const userId = req.user.userId;
    const userBranch = req.user.branchId;
    
    let hasAccess = false;
    
    switch (userRole) {
      case 'super_admin':
      case 'general_manager':
        hasAccess = true;
        break;
      case 'deputy_manager':
        hasAccess = formData.submittedBy.toString() === userId.toString();
        break;
      case 'branch_manager':
        hasAccess = formData.branch.toString() === userBranch?.toString();
        break;
    }
    
    if (!hasAccess) {
      return res.status(403).json({
        success: false,
        message: 'Access denied to this form data'
      });
    }
    
    res.json({
      success: true,
      data: {
        formDataId: formData._id,
        loanType: formData.loanType,
        formData: formData.formData,
        mappedData: formData.mappedData,
        status: formData.status,
        version: formData.version,
        validationResults: formData.validationResults,
        pdfStatus: formData.pdfStatus,
        attachments: formData.attachments,
        submissionMetadata: formData.submissionMetadata,
        changeHistory: formData.changeHistory,
        schema: formData.schemaDetails
      }
    });
    
  } catch (error) {
    console.error('Error getting form data:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get form data',
      error: error.message
    });
  }
};

/**
 * Validate form data
 */
const validateFormData = async (req, res) => {
  try {
    const { workflowId } = req.params;
    
    const formData = await LoanFormData.findById(workflowId);
    if (!formData) {
      return res.status(404).json({
        success: false,
        message: 'Form data not found'
      });
    }
    
    const validationResults = await formData.validateFormData();
    
    res.json({
      success: true,
      message: 'Form validation completed',
      data: {
        isValid: validationResults.isValid,
        errors: validationResults.errors,
        warnings: validationResults.warnings,
        status: formData.status
      }
    });
    
  } catch (error) {
    console.error('Error validating form data:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to validate form data',
      error: error.message
    });
  }
};

/**
 * Get user's form submissions
 */
const getUserFormSubmissions = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { status, limit = 10, page = 1 } = req.query;
    
    const skip = (page - 1) * limit;
    
    let query = { submittedBy: userId, isActive: true };
    if (status) query.status = status;
    
    const formSubmissions = await LoanFormData.find(query)
      .populate('formSchema', 'loanTypeName version')
      .populate('branch', 'name code')
      .sort({ updatedAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));
    
    const total = await LoanFormData.countDocuments(query);
    
    const submissions = formSubmissions.map(submission => ({
      formDataId: submission._id,
      workflowId: submission.workflowId,
      loanType: submission.loanType,
      loanTypeName: submission.formSchema?.loanTypeName,
      status: submission.status,
      version: submission.version,
      submittedAt: submission.createdAt,
      updatedAt: submission.updatedAt,
      branch: submission.branch,
      validationResults: submission.validationResults,
      pdfStatus: submission.pdfStatus
    }));
    
    res.json({
      success: true,
      data: {
        submissions,
        pagination: {
          current: parseInt(page),
          total: Math.ceil(total / limit),
          count: submissions.length,
          totalRecords: total
        }
      }
    });
    
  } catch (error) {
    console.error('Error getting user form submissions:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get form submissions',
      error: error.message
    });
  }
};

module.exports = {
  getFormSchema,
  getAllFormSchemas,
  saveFormData,
  getFormData,
  validateFormData,
  getUserFormSubmissions
};
