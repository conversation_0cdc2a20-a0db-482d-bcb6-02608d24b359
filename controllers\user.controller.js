const User = require("../models/user.model");
const SuperAdmin = require("../schema/superadmin.model");
const jwt = require("jsonwebtoken");
const { JWT_SECRET } = process.env;

// Middleware to check if user is super_admin
const isSuperAdmin = async (req, res, next) => {
  try {
    // Check if user role is already super_admin from token
    if (req.user && req.user.role === "super_admin") {
      return next();
    }

    // If not, try to find user in database
    const userId = req.user.userId || req.user.id;

    // Check SuperAdmin collection first
    let user = await SuperAdmin.findById(userId);

    // If not found in SuperAdmin, check User collection
    if (!user) {
      user = await User.findById(userId);
    }

    if (!user) {
      return res
        .status(403)
        .json({ message: "Access denied. User not found." });
    }

    if (user.role !== "super_admin") {
      return res
        .status(403)
        .json({ message: "Access denied. Super admin privileges required." });
    }

    next();
  } catch (error) {
    next(error);
  }
};

// Get all users with optional status filter
const getAllUsers = async (req, res) => {
  try {
    const { isActive } = req.query;

    // Build query based on isActive parameter
    const query = {};
    if (isActive !== undefined) {
      query.isActive = isActive === "true";
    }

    const users = await User.find(query, "-password");
    res.json(users);
  } catch (error) {
    res
      .status(500)
      .json({ message: "Error fetching users", error: error.message });
  }
};

// Create new user (only superadmin)
const createUser = async (req, res) => {
  try {
    const { email, password, role, firstName, lastName } = req.body;

    // Validate role
    if (
      !["employee", "manager", "branchmanager", "managingdirector"].includes(
        role
      )
    ) {
      return res.status(400).json({ message: "Invalid role" });
    }

    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res.status(400).json({ message: "User already exists" });
    }

    const user = new User({
      email,
      password,
      role,
      firstName,
      lastName,
    });

    await user.save();
    res.status(201).json({
      message: "User created successfully",
      user: user.toObject({ getters: true }),
    });
  } catch (error) {
    res
      .status(500)
      .json({ message: "Error creating user", error: error.message });
  }
};

// Update user role (only superadmin)
const updateUserRole = async (req, res) => {
  try {
    const { userId, newRole } = req.body;

    // Validate role
    if (
      !["deputy_manager", "branch_manager", "general_manager"].includes(newRole)
    ) {
      return res.status(400).json({ message: "Invalid role" });
    }

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }

    user.role = newRole;
    await user.save();

    res.json({
      message: "User role updated successfully",
      user: user.toObject({ getters: true }),
    });
  } catch (error) {
    res
      .status(500)
      .json({ message: "Error updating user role", error: error.message });
  }
};

// Update user status (only superadmin)
const updateUserStatus = async (req, res) => {
  try {
    const { userId } = req.params;
    const { isActive } = req.body;

    // Validate isActive parameter
    if (typeof isActive !== "boolean") {
      return res
        .status(400)
        .json({ message: "isActive must be a boolean value" });
    }

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }

    // Update user status
    user.isActive = isActive;
    await user.save();

    res.json({
      message: `User ${isActive ? "activated" : "deactivated"} successfully`,
      user: user.toObject({ getters: true }),
    });
  } catch (error) {
    res
      .status(500)
      .json({ message: "Error updating user status", error: error.message });
  }
};

module.exports = {
  isSuperAdmin,
  getAllUsers,
  createUser,
  updateUserRole,
  updateUserStatus,
};
