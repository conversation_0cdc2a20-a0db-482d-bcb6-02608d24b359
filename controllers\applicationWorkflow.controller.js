const { PrimaryApplication, APPLICATION_STATUS, APPROVAL_ACTION } = require('../schema/primaryApplication.model');
const { ErrorHandler } = require('../utils/common');

class ApplicationWorkflowController {
  static async submitApplication(applicationId, userId, role, comments = '') {
    return this.updateApplicationStatus({
      applicationId,
      userId,
      role,
      action: APPROVAL_ACTION.SUBMIT,
      targetStatus: APPLICATION_STATUS.EMPLOYEE_REVIEW,
      allowedRoles: ['employee', 'manager', 'super_admin'],
      comments
    });
  }

  static async reviewApplication({
    applicationId,
    userId,
    role,
    action,
    comments = '',
    nextReviewerId = null
  }) {
    const application = await PrimaryApplication.findById(applicationId)
      .populate('createdBy', 'name email')
      .populate('currentReviewer', 'name email');

    if (!application) {
      throw new ErrorHandler(404, 'Application not found');
    }

    // CEO can bypass all levels
    const isCeo = role === 'super_admin';
    let targetStatus = application.status;
    
    if (action === APPROVAL_ACTION.APPROVE) {
      targetStatus = PrimaryApplication.getNextStatus(application.status, isCeo);
    } else if (action === APPROVAL_ACTION.REJECT) {
      targetStatus = APPLICATION_STATUS.REJECTED;
    } else if (action === APPROVAL_ACTION.REQUEST_CHANGES) {
      targetStatus = APPLICATION_STATUS.DRAFT;
    } else if (action === APPROVAL_ACTION.BYPASS && isCeo) {
      targetStatus = APPLICATION_STATUS.APPROVED;
    }

    // Update application status and record history
    application.status = targetStatus;
    application.approvalHistory.push({
      action,
      performedBy: userId,
      role,
      comments,
      status: targetStatus
    });

    // Set next reviewer if applicable
    if (targetStatus === APPLICATION_STATUS.EMPLOYEE_REVIEW && nextReviewerId) {
      application.currentReviewer = nextReviewerId;
    } else if (targetStatus === APPLICATION_STATUS.MANAGER_REVIEW && nextReviewerId) {
      application.currentReviewer = nextReviewerId;
    } else {
      application.currentReviewer = undefined;
    }

    await application.save();
    
    // TODO: Trigger notifications
    // await this.triggerNotifications(application, action, role);

    return application;
  }

  static async getApplicationWorkflow(applicationId, userId, role) {
    const application = await PrimaryApplication.findById(applicationId)
      .populate('createdBy', 'name email')
      .populate('currentReviewer', 'name email')
      .populate('approvalHistory.performedBy', 'name email role');

    if (!application) {
      throw new ErrorHandler(404, 'Application not found');
    }

    // Authorization check
    if (
      role !== 'super_admin' && 
      application.createdBy._id.toString() !== userId.toString() &&
      (!application.currentReviewer || application.currentReviewer._id.toString() !== userId.toString())
    ) {
      throw new ErrorHandler(403, 'Not authorized to view this application');
    }

    return application;
  }

  static async getApplicationsForReview(userId, role, status) {
    const query = {};
    
    if (status) {
      query.status = status;
    } else {
      // Default to showing applications in review or pending review
      query.status = { 
        $in: [
          APPLICATION_STATUS.EMPLOYEE_REVIEW,
          APPLICATION_STATUS.MANAGER_REVIEW,
          APPLICATION_STATUS.CEO_APPROVAL
        ] 
      };
    }

    // Get applications where user is the current reviewer or has appropriate role
    if (role === 'employee') {
      query.$or = [
        { status: APPLICATION_STATUS.EMPLOYEE_REVIEW },
        { currentReviewer: userId }
      ];
    } else if (role === 'manager') {
      query.$or = [
        { status: APPLICATION_STATUS.MANAGER_REVIEW },
        { currentReviewer: userId }
      ];
    } else if (role !== 'superadmin') {
      // Only show user's own applications for other roles
      query.createdBy = userId;
    }

    return PrimaryApplication.find(query)
      .populate('createdBy', 'name email')
      .populate('currentReviewer', 'name email')
      .sort({ updatedAt: -1 });
  }

  static async getApplicationHistory(applicationId, userId, role) {
    const application = await PrimaryApplication.findById(applicationId)
      .select('approvalHistory createdBy')
      .populate('approvalHistory.performedBy', 'name email role');

    if (!application) {
      throw new ErrorHandler(404, 'Application not found');
    }

    // Authorization check
    if (
      role !== 'super_admin' && 
      application.createdBy.toString() !== userId.toString()
    ) {
      throw new ErrorHandler(403, 'Not authorized to view this application history');
    }

    return application.approvalHistory.sort((a, b) => b.timestamp - a.timestamp);
  }

  // Helper method for status updates with validation
  static async updateApplicationStatus({
    applicationId,
    userId,
    role,
    action,
    targetStatus,
    allowedRoles,
    comments = ''
  }) {
    const application = await PrimaryApplication.findById(applicationId);
    
    if (!application) {
      throw new ErrorHandler(404, 'Application not found');
    }

    // Validate role permissions
    if (allowedRoles && !allowedRoles.includes(role)) {
      throw new ErrorHandler(403, 'Insufficient permissions for this action');
    }

    // Validate status transition
    if (targetStatus !== undefined) {
      application.status = targetStatus;
    }

    // Record history
    application.approvalHistory.push({
      action,
      performedBy: userId,
      role,
      comments,
      status: application.status
    });

    await application.save();
    return application;
  }
}

module.exports = ApplicationWorkflowController;
