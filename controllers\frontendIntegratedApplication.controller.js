const { LoanWorkflow, WOR<PERSON><PERSON>OW_STATUS, WORK<PERSON>OW_ACTIONS } = require('../models/loanWorkflow.model');
const LoanFormData = require('../models/loanFormData.model');
const { PrimaryApplication } = require('../schema/primaryApplication.model');
const { 
  transformFrontendToBackend, 
  generatePrimaryApplicationData, 
  generatePDFTemplateData,
  validateFrontendData 
} = require('../utils/frontendBackendMapping');
const pdfGeneratorService = require('../services/pdfGenerator.service');
const { generateFrontendResponse } = require('../utils/frontendHelper');

/**
 * Create loan application using your frontend schema format
 */
const createApplicationFromFrontend = async (req, res) => {
  try {
    const { loanType, formData, isDraft = true } = req.body;
    const userId = req.user.userId;
    const branchId = req.user.branchId;
    
    // Validate user role
    if (req.user.role !== 'deputy_manager') {
      return res.status(403).json({
        success: false,
        message: 'Only deputy managers can create loan applications'
      });
    }
    
    // Validate frontend form data
    const validationErrors = validateFrontendData(formData);
    if (validationErrors.length > 0 && !isDraft) {
      return res.status(400).json({
        success: false,
        message: 'Form validation failed',
        errors: validationErrors
      });
    }
    
    // Transform frontend data to backend format
    const backendFormData = transformFrontendToBackend(formData);
    
    // Extract loan amount for workflow
    const loanAmount = formData.loanAmount || backendFormData.loanAmount;
    if (!loanAmount) {
      return res.status(400).json({
        success: false,
        message: 'Loan amount is required'
      });
    }
    
    // Create workflow
    const workflow = new LoanWorkflow({
      loanAmount: Number(loanAmount),
      createdBy: userId,
      branch: branchId,
      currentStatus: WORKFLOW_STATUS.DRAFT,
      workflowHistory: [{
        action: WORKFLOW_ACTIONS.SAVE_DRAFT,
        fromStatus: null,
        toStatus: WORKFLOW_STATUS.DRAFT,
        performedBy: userId,
        performedByRole: req.user.role,
        loanAmount: Number(loanAmount),
        comments: `${loanType} application created`
      }]
    });
    
    await workflow.save();
    
    // Create form data with both frontend and backend formats
    const loanFormData = new LoanFormData({
      formSchema: null, // We'll use direct mapping instead of schema
      workflowId: workflow._id,
      loanType,
      formData: backendFormData, // Store transformed data
      originalFormData: formData, // Store original frontend data
      submittedBy: userId,
      branch: branchId,
      status: isDraft ? 'draft' : 'submitted',
      submissionMetadata: {
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
        frontendVersion: '2.0.0'
      }
    });
    
    // Generate mapped data for primary application
    if (!isDraft) {
      const primaryAppData = generatePrimaryApplicationData(formData);
      loanFormData.mappedData = primaryAppData;
      
      // Create primary application record
      const primaryApp = new PrimaryApplication({
        applicationId: workflow.applicationId,
        ...primaryAppData,
        createdBy: userId,
        status: 1 // DRAFT status
      });
      
      await primaryApp.save();
      workflow.applicationData = primaryApp._id;
    }
    
    await loanFormData.addChangeHistory(
      'created',
      userId,
      { formData: backendFormData },
      isDraft ? 'Application created as draft' : 'Application created and submitted'
    );
    
    await workflow.save();
    await loanFormData.save();
    
    // Generate frontend response
    const frontendData = generateFrontendResponse(workflow, req.user.role, userId, false);
    
    res.status(201).json({
      success: true,
      message: isDraft ? 'Application saved as draft' : 'Application created successfully',
      data: {
        workflowId: workflow._id,
        applicationId: workflow.applicationId,
        formDataId: loanFormData._id,
        loanType,
        loanAmount,
        status: workflow.currentStatus,
        formStatus: loanFormData.status,
        validationErrors: validationErrors.length > 0 ? validationErrors : null,
        ui: frontendData.ui,
        metadata: frontendData.metadata
      }
    });
    
  } catch (error) {
    console.error('Error creating application from frontend:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create application',
      error: error.message
    });
  }
};

/**
 * Update application using frontend schema format
 */
const updateApplicationFromFrontend = async (req, res) => {
  try {
    const { workflowId } = req.params;
    const { formData, isDraft = true } = req.body;
    const userId = req.user.userId;
    
    // Get workflow and form data
    const workflow = await LoanWorkflow.findById(workflowId);
    if (!workflow) {
      return res.status(404).json({
        success: false,
        message: 'Application not found'
      });
    }
    
    // Check permissions
    if (req.user.role === 'deputy_manager' && workflow.createdBy.toString() !== userId.toString()) {
      return res.status(403).json({
        success: false,
        message: 'You can only update your own applications'
      });
    }
    
    // Check if application can be edited
    if (![WORKFLOW_STATUS.DRAFT, WORKFLOW_STATUS.RETURNED_FOR_CHANGES].includes(workflow.currentStatus)) {
      return res.status(400).json({
        success: false,
        message: 'Application cannot be edited in current status'
      });
    }
    
    // Validate frontend form data
    const validationErrors = validateFrontendData(formData);
    if (validationErrors.length > 0 && !isDraft) {
      return res.status(400).json({
        success: false,
        message: 'Form validation failed',
        errors: validationErrors
      });
    }
    
    // Transform frontend data to backend format
    const backendFormData = transformFrontendToBackend(formData);
    
    const loanFormData = await LoanFormData.getByWorkflowId(workflowId);
    if (!loanFormData) {
      return res.status(404).json({
        success: false,
        message: 'Form data not found'
      });
    }
    
    // Update form data
    loanFormData.formData = { ...loanFormData.formData, ...backendFormData };
    loanFormData.originalFormData = formData;
    loanFormData.status = isDraft ? 'draft' : 'submitted';
    loanFormData.version += 1;
    
    // Update loan amount in workflow if changed
    if (formData.loanAmount && formData.loanAmount !== workflow.loanAmount) {
      workflow.loanAmount = Number(formData.loanAmount);
    }
    
    // Generate mapped data if not draft
    if (!isDraft) {
      const primaryAppData = generatePrimaryApplicationData(formData);
      loanFormData.mappedData = primaryAppData;
    }
    
    await loanFormData.addChangeHistory(
      'updated',
      userId,
      { formData: backendFormData },
      isDraft ? 'Application updated as draft' : 'Application updated and submitted'
    );
    
    await workflow.save();
    await loanFormData.save();
    
    // Generate frontend response
    const frontendData = generateFrontendResponse(workflow, req.user.role, userId, false);
    
    res.json({
      success: true,
      message: isDraft ? 'Application updated as draft' : 'Application updated successfully',
      data: {
        workflowId: workflow._id,
        formDataId: loanFormData._id,
        version: loanFormData.version,
        status: workflow.currentStatus,
        formStatus: loanFormData.status,
        validationErrors: validationErrors.length > 0 ? validationErrors : null,
        ui: frontendData.ui
      }
    });
    
  } catch (error) {
    console.error('Error updating application from frontend:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update application',
      error: error.message
    });
  }
};

/**
 * Generate PDF using frontend data format
 */
const generatePDFFromFrontend = async (req, res) => {
  try {
    const { workflowId } = req.params;
    
    const loanFormData = await LoanFormData.getByWorkflowId(workflowId);
    if (!loanFormData) {
      return res.status(404).json({
        success: false,
        message: 'Application not found'
      });
    }
    
    // Generate PDF template data from original frontend data
    const frontendData = loanFormData.originalFormData || loanFormData.formData;
    const pdfData = generatePDFTemplateData(frontendData);
    
    // Add metadata
    pdfData.application_id = workflowId;
    pdfData.generated_at = new Date().toLocaleString();
    pdfData.loan_type = 'Vehicle Loan Application';
    
    // Generate PDF using our service (we'll create a custom template)
    const result = await generateCustomPDF(pdfData, loanFormData.loanType);
    
    // Update form data with PDF info
    loanFormData.pdfStatus = {
      isGenerated: true,
      pdfPath: result.pdfPath,
      pdfUrl: result.pdfUrl,
      generatedAt: new Date()
    };
    
    await loanFormData.save();
    
    res.json({
      success: true,
      message: 'PDF generated successfully',
      data: {
        pdfUrl: result.pdfUrl,
        fileName: result.fileName
      }
    });
    
  } catch (error) {
    console.error('Error generating PDF from frontend:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate PDF',
      error: error.message
    });
  }
};

/**
 * Custom PDF generation for frontend data
 */
const generateCustomPDF = async (pdfData, loanType) => {
  // Use the existing PDF generator service with custom template
  const puppeteer = require('puppeteer');
  const handlebars = require('handlebars');
  const fs = require('fs').promises;
  const path = require('path');
  
  const outputDir = path.join(__dirname, '../uploads/pdfs');
  await fs.mkdir(outputDir, { recursive: true });
  
  // Create custom template for your frontend data
  const template = handlebars.compile(getVehicleLoanTemplate());
  const html = template(pdfData);
  
  const browser = await puppeteer.launch({ headless: true });
  const page = await browser.newPage();
  
  await page.setContent(html, { waitUntil: 'networkidle0' });
  
  const pdfBuffer = await page.pdf({
    format: 'A4',
    margin: { top: '20mm', right: '20mm', bottom: '20mm', left: '20mm' },
    printBackground: true
  });
  
  await page.close();
  await browser.close();
  
  const fileName = `vehicle_loan_${Date.now()}.pdf`;
  const filePath = path.join(outputDir, fileName);
  
  await fs.writeFile(filePath, pdfBuffer);
  
  return {
    pdfPath: filePath,
    pdfUrl: `/uploads/pdfs/${fileName}`,
    fileName
  };
};

/**
 * Vehicle loan PDF template
 */
const getVehicleLoanTemplate = () => {
  return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Vehicle Loan Application</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; line-height: 1.4; }
        .header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 20px; margin-bottom: 30px; }
        .section { margin-bottom: 25px; page-break-inside: avoid; }
        .section-title { background-color: #f8f9fa; padding: 12px; font-weight: bold; margin-bottom: 15px; border-left: 4px solid #007bff; }
        .field-row { display: flex; margin-bottom: 8px; }
        .field-label { width: 180px; font-weight: bold; color: #333; }
        .field-value { flex: 1; color: #666; }
        .signature-section { margin-top: 50px; display: flex; justify-content: space-between; }
        .signature-box { width: 200px; text-align: center; border-top: 1px solid #333; padding-top: 10px; }
        .grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>VEHICLE LOAN APPLICATION</h1>
        <p><strong>Application ID:</strong> {{application_id}}</p>
        <p><strong>Generated:</strong> {{generated_at}}</p>
    </div>

    <div class="section">
        <div class="section-title">APPLICANT PERSONAL INFORMATION</div>
        <div class="grid">
            <div>
                <div class="field-row">
                    <div class="field-label">Full Name:</div>
                    <div class="field-value">{{applicant_name}}</div>
                </div>
                <div class="field-row">
                    <div class="field-label">Father's Name:</div>
                    <div class="field-value">{{father_name}}</div>
                </div>
                <div class="field-row">
                    <div class="field-label">Date of Birth:</div>
                    <div class="field-value">{{date_of_birth}}</div>
                </div>
                <div class="field-row">
                    <div class="field-label">Age:</div>
                    <div class="field-value">{{applicant_age}} years</div>
                </div>
            </div>
            <div>
                <div class="field-row">
                    <div class="field-label">Gender:</div>
                    <div class="field-value">{{applicant_gender}}</div>
                </div>
                <div class="field-row">
                    <div class="field-label">Marital Status:</div>
                    <div class="field-value">{{marital_status}}</div>
                </div>
                <div class="field-row">
                    <div class="field-label">Mobile:</div>
                    <div class="field-value">{{mobile_number}}</div>
                </div>
                <div class="field-row">
                    <div class="field-label">Email:</div>
                    <div class="field-value">{{email_address}}</div>
                </div>
            </div>
        </div>
        <div class="field-row">
            <div class="field-label">Address:</div>
            <div class="field-value">{{permanent_address}}</div>
        </div>
    </div>

    <div class="section">
        <div class="section-title">VEHICLE INFORMATION</div>
        <div class="grid">
            <div>
                <div class="field-row">
                    <div class="field-label">Vehicle Type:</div>
                    <div class="field-value">{{vehicle_type}}</div>
                </div>
                <div class="field-row">
                    <div class="field-label">Make/Brand:</div>
                    <div class="field-value">{{vehicle_make}}</div>
                </div>
                <div class="field-row">
                    <div class="field-label">Model:</div>
                    <div class="field-value">{{vehicle_model}}</div>
                </div>
                <div class="field-row">
                    <div class="field-label">Variant:</div>
                    <div class="field-value">{{vehicle_variant}}</div>
                </div>
            </div>
            <div>
                <div class="field-row">
                    <div class="field-label">Manufacturing Year:</div>
                    <div class="field-value">{{manufacturing_year}}</div>
                </div>
                <div class="field-row">
                    <div class="field-label">Condition:</div>
                    <div class="field-value">{{vehicle_condition}}</div>
                </div>
                <div class="field-row">
                    <div class="field-label">Vehicle Price:</div>
                    <div class="field-value">{{vehicle_price}}</div>
                </div>
                <div class="field-row">
                    <div class="field-label">Dealer Name:</div>
                    <div class="field-value">{{dealer_name}}</div>
                </div>
            </div>
        </div>
    </div>

    <div class="section">
        <div class="section-title">LOAN REQUIREMENTS</div>
        <div class="grid">
            <div>
                <div class="field-row">
                    <div class="field-label">Loan Amount:</div>
                    <div class="field-value">{{loan_amount}}</div>
                </div>
                <div class="field-row">
                    <div class="field-label">Down Payment:</div>
                    <div class="field-value">{{down_payment}}</div>
                </div>
            </div>
            <div>
                <div class="field-row">
                    <div class="field-label">Loan Tenure:</div>
                    <div class="field-value">{{loan_tenure}} years</div>
                </div>
                <div class="field-row">
                    <div class="field-label">Interest Type:</div>
                    <div class="field-value">{{interest_type}}</div>
                </div>
            </div>
        </div>
    </div>

    <div class="signature-section">
        <div class="signature-box">
            <div>Applicant Signature</div>
        </div>
        <div class="signature-box">
            <div>Branch Manager</div>
        </div>
        <div class="signature-box">
            <div>Date</div>
        </div>
    </div>
</body>
</html>`;
};

module.exports = {
  createApplicationFromFrontend,
  updateApplicationFromFrontend,
  generatePDFFromFrontend
};
