const express = require('express');
const router = express.Router();
const loanController = require('../controllers/loan.controller');
const { validateLoan, validateLoanUpdate } = require('../validators/loans');
const { authenticate } = require('../middleware/auth.middleware');
const { validate } = require('../middleware/validation.middleware');
const { body } = require('express-validator');

// Submit new loan application
router.post('/', authenticate, validateLoan, loanController.createLoan);

// Update existing loan application
router.patch('/:id', authenticate, validateLoanUpdate, loanController.updateLoan);

// Get loan application by ID
router.get('/:id', authenticate, loanController.getLoanById);

// List all loan applications (with filtering/pagination)
router.get('/', authenticate, loanController.listLoans);

// Update loan application status (submit, approve, reject, return, disburse)
router.post(
  '/:id/status',
  authenticate,
  [
    body('action')
      .isIn(['submit', 'approve', 'reject', 'return', 'disburse'])
      .withMessage('Invalid action'),
    body('comments').optional().isString().trim()
  ],
  validate,
  loanController.transitionStatus
);

module.exports = router;
