# 🎯 Dynamic Form & PDF Generation System

## 📋 Overview

This document describes the comprehensive dynamic form generation and PDF creation system for the LOS (Loan Origination System). The system handles different loan types with unique form schemas, validates data, maps to primary application format, and generates PDFs using Puppeteer and Handlebars.

## 🏗️ System Architecture

### **Core Components:**

1. **Dynamic Form Schema System** - Flexible schema definition for different loan types
2. **Form Data Processing** - Validation, mapping, and storage
3. **Key-Value Mapping System** - Ensures consistency between frontend and backend
4. **PDF Generation Service** - Puppeteer + Handlebars for PDF creation
5. **Integrated Workflow** - Seamless integration with loan approval workflow

## 🎨 Dynamic Form Schema System

### **Schema Structure:**
```javascript
{
  loanType: 'vehicle_loan',
  loanTypeName: 'Vehicle Loan',
  version: '1.0.0',
  sections: [
    {
      sectionName: 'personal_info',
      title: 'Personal Information',
      order: 1,
      fields: [...]
    }
  ],
  fields: [
    {
      fieldName: 'firstName',
      fieldType: 'text',
      label: 'First Name',
      isRequired: true,
      validation: { minLength: 2, maxLength: 50 },
      primaryApplicationMapping: { fieldPath: 'personalInfo.fname' },
      pdfMapping: { templateVariable: 'applicant_first_name' }
    }
  ],
  pdfTemplate: {
    templateName: 'vehicle_loan_application',
    templatePath: './templates/pdf/vehicle_loan.hbs'
  }
}
```

### **Supported Field Types:**
- `text` - Text input
- `number` - Numeric input
- `email` - Email validation
- `phone` - Phone number validation
- `date` - Date picker
- `select` - Dropdown selection
- `radio` - Radio buttons
- `checkbox` - Checkboxes
- `textarea` - Multi-line text
- `file` - File upload
- `currency` - Currency input
- `percentage` - Percentage input

### **Validation Rules:**
- `minLength` / `maxLength` - String length validation
- `min` / `max` - Numeric range validation
- `pattern` - Regex pattern validation
- `customValidation` - Custom validation functions

## 🔄 Key-Value Mapping System

### **Three-Layer Mapping:**

#### **1. Frontend Form Fields → Primary Application**
```javascript
// Standard mappings for common fields
'firstName' → 'personalInfo.fname'
'phoneNumber' → 'phone.number'
'loanAmount' → 'loanInfo.amount'
'homeAddressLine1' → 'homeAddress.line1'
```

#### **2. Loan Type Specific Mappings**
```javascript
// Vehicle loan specific fields
'vehicleType' → 'loanSpecific.vehicleType'
'vehicleMake' → 'loanSpecific.vehicleMake'
'vehiclePrice' → 'loanSpecific.vehiclePrice'

// Housing loan specific fields
'propertyType' → 'loanSpecific.propertyType'
'propertyValue' → 'loanSpecific.propertyValue'
```

#### **3. PDF Template Variables**
```javascript
// PDF template mappings
'firstName' → 'applicant_first_name'
'loanAmount' → 'loan_amount'
'vehiclePrice' → 'vehicle_price'
```

### **Auto-Mapping Features:**
- Automatic field mapping generation
- Validation of mapping consistency
- Support for data transformers and formatters

## 📄 PDF Generation System

### **Technology Stack:**
- **Puppeteer** - Headless Chrome for PDF generation
- **Handlebars** - Template engine for HTML generation
- **Custom Helpers** - Currency, date, phone formatting

### **PDF Generation Process:**
1. **Data Preparation** - Extract form data and apply formatters
2. **Template Selection** - Choose appropriate Handlebars template
3. **HTML Generation** - Compile template with data
4. **PDF Creation** - Use Puppeteer to convert HTML to PDF
5. **File Storage** - Save PDF and update form data status

### **Handlebars Helpers:**
```javascript
{{formatCurrency amount}}     // ₹5,00,000
{{formatDate dateOfBirth}}    // 15/08/1990
{{formatPhone phoneNumber}}   // ************
{{uppercase name}}            // JOHN DOE
{{capitalize status}}         // Approved
```

### **Template Structure:**
```html
<!DOCTYPE html>
<html>
<head>
    <title>{{loanTypeName}} Application</title>
    <style>/* PDF-specific styles */</style>
</head>
<body>
    <div class="header">
        <h1>{{uppercase loanTypeName}} APPLICATION</h1>
        <p>Application ID: {{applicationId}}</p>
    </div>
    
    <div class="section">
        <h2>Personal Information</h2>
        <p>Name: {{applicant_first_name}} {{applicant_last_name}}</p>
        <p>Email: {{applicant_email}}</p>
        <p>Phone: {{formatPhone applicant_phone}}</p>
    </div>
    
    <div class="section">
        <h2>Loan Details</h2>
        <p>Amount: {{formatCurrency loan_amount}}</p>
        <p>Purpose: {{loan_purpose}}</p>
    </div>
</body>
</html>
```

## 🚀 API Endpoints

### **Form Schema Management:**
```http
GET /loan-forms/schemas                    # Get all form schemas
GET /loan-forms/schema/{loanType}          # Get specific schema
```

### **Form Data Operations:**
```http
POST /loan-forms/data                      # Save/update form data
GET /loan-forms/data/{workflowId}          # Get form data
POST /loan-forms/validate/{workflowId}     # Validate form data
GET /loan-forms/my-submissions             # Get user submissions
```

### **PDF Operations:**
```http
POST /loan-forms/generate-pdf/{formDataId}    # Generate PDF
GET /loan-forms/download-pdf/{formDataId}     # Download PDF
```

### **Integrated Application APIs:**
```http
POST /applications                         # Create complete application
GET /applications/{workflowId}             # Get application details
PUT /applications/{workflowId}             # Update application
POST /applications/{workflowId}/submit     # Submit with PDF generation
POST /applications/{workflowId}/pdf        # Generate PDF
GET /applications/{workflowId}/pdf/download # Download PDF
```

## 🎯 Frontend Integration Guide

### **1. Get Form Schema:**
```javascript
const response = await fetch('/loan-forms/schema/vehicle_loan', {
  headers: { 'Authorization': `Bearer ${token}` }
});
const { data } = await response.json();

// data.sections contains organized form structure
// data.sections[0].fields contains field definitions
```

### **2. Render Dynamic Form:**
```javascript
data.sections.forEach(section => {
  const sectionElement = createSection(section.title);
  
  section.fields.forEach(field => {
    const fieldElement = createField({
      type: field.fieldType,
      name: field.fieldName,
      label: field.label,
      required: field.isRequired,
      validation: field.validation,
      options: field.options
    });
    
    sectionElement.appendChild(fieldElement);
  });
});
```

### **3. Submit Form Data:**
```javascript
const formData = {
  firstName: 'John',
  lastName: 'Doe',
  email: '<EMAIL>',
  phoneNumber: '9876543210',
  loanAmount: 500000,
  vehicleType: 'four_wheeler',
  vehicleMake: 'Maruti',
  vehicleModel: 'Swift'
};

const response = await fetch('/applications', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({
    loanType: 'vehicle_loan',
    formData,
    isDraft: false
  })
});

const result = await response.json();
// result.data contains workflow info and UI states
```

### **4. Handle UI States:**
```javascript
const { ui } = result.data;

// Render action buttons based on UI state
Object.entries(ui.buttons).forEach(([key, button]) => {
  if (button.visible) {
    const buttonElement = createButton({
      text: button.text,
      variant: button.variant,
      enabled: button.enabled,
      onClick: () => handleAction(key)
    });
    
    if (button.required) {
      buttonElement.classList.add('required');
    }
  }
});

// Show notifications
ui.notifications.forEach(notification => {
  showNotification(notification.type, notification.message);
});

// Render progress indicator
renderProgress(ui.progress);
```

## 🔧 Configuration & Setup

### **1. Install Dependencies:**
```bash
npm install puppeteer handlebars
```

### **2. Environment Variables:**
```env
# PDF Generation
PDF_OUTPUT_DIR=./uploads/pdfs
PDF_TEMPLATE_DIR=./templates/pdf
PUPPETEER_HEADLESS=true

# File Upload
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=5242880  # 5MB
```

### **3. Directory Structure:**
```
/templates/pdf/
  ├── vehicle_loan.hbs
  ├── personal_loan.hbs
  ├── housing_loan.hbs
  └── default.hbs

/uploads/pdfs/
  └── generated PDFs

/models/
  ├── loanFormSchema.model.js
  └── loanFormData.model.js

/services/
  └── pdfGenerator.service.js

/utils/
  └── formMapping.js
```

## 🎨 Loan Type Examples

### **Vehicle Loan Schema:**
- Personal Information (name, email, phone, DOB)
- Contact Information (address details)
- Vehicle Details (type, make, model, price, down payment)
- Financial Information (income, company, experience)
- Loan Details (amount, tenure)

### **Personal Loan Schema:**
- Personal Information (basic details)
- Employment Details (company, income)
- Loan Requirements (amount, purpose)

### **Housing Loan Schema:**
- Personal Information
- Property Details (type, value, address, construction status)
- Financial Information
- Loan Details

## 🔒 Security & Validation

### **Data Validation:**
- Field-level validation (type, length, pattern)
- Cross-field validation rules
- Business rule validation
- Required field enforcement

### **Access Control:**
- Role-based form access
- User-specific data visibility
- Branch-based restrictions
- Workflow state permissions

### **File Security:**
- PDF file access control
- Secure file storage
- Download authorization
- File cleanup policies

## 📊 Benefits & Features

### ✅ **Dynamic & Flexible:**
- Easy addition of new loan types
- Configurable form fields and validation
- Version control for form schemas

### ✅ **Consistent Mapping:**
- Automatic field mapping generation
- Validation of mapping consistency
- Support for data transformations

### ✅ **Professional PDFs:**
- Loan type specific templates
- Proper formatting and styling
- Digital signatures support

### ✅ **Workflow Integration:**
- Seamless workflow integration
- Status-based form access
- Automatic PDF generation on submission

### ✅ **Frontend Ready:**
- Complete UI state information
- Dynamic form rendering support
- Real-time validation feedback

## 🚀 Future Enhancements

- **Digital Signatures** - E-signature integration
- **Document Attachments** - File upload with form data
- **Template Editor** - Visual PDF template designer
- **Multi-language Support** - Internationalization
- **Advanced Validation** - Complex business rules
- **Audit Trail** - Complete form change history

This dynamic form and PDF generation system provides a robust, scalable foundation for handling diverse loan application requirements with professional document generation capabilities! 🎯
