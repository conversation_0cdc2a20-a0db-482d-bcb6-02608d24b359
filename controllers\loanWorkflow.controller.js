const { LoanWorkflow, WOR<PERSON><PERSON>OW_STATUS, WOR<PERSON><PERSON><PERSON>_ACTIONS, LOAN_AMOUNT_THRESHOLD } = require('../models/loanWorkflow.model');
const { PrimaryApplication } = require('../schema/primaryApplication.model');
const User = require('../models/user.model');
const Branch = require('../models/branch.model');
const { generateFrontendResponse } = require('../utils/frontendHelper');

/**
 * Create a new loan workflow
 */
const createLoanWorkflow = async (req, res) => {
  try {
    const { applicationData, loanAmount, branchId } = req.body;
    const userId = req.user.userId;
    
    // Validate user role
    if (req.user.role !== 'deputy_manager') {
      return res.status(403).json({
        success: false,
        message: 'Only deputy managers can create loan applications'
      });
    }
    
    // Create workflow
    const workflow = new LoanWorkflow({
      loanAmount,
      createdBy: userId,
      branch: branchId || req.user.branchId,
      applicationData,
      currentStatus: WORKFLOW_STATUS.DRAFT,
      workflowHistory: [{
        action: WORKFLOW_ACTIONS.SAVE_DRAFT,
        fromStatus: null,
        toStatus: WORKFLOW_STATUS.DRAFT,
        performedBy: userId,
        performedByRole: req.user.role,
        loanAmount,
        comments: 'Application created'
      }]
    });
    
    await workflow.save();
    
    res.status(201).json({
      success: true,
      message: 'Loan workflow created successfully',
      data: {
        workflowId: workflow._id,
        applicationId: workflow.applicationId,
        currentStatus: workflow.currentStatus,
        loanAmount: workflow.loanAmount
      }
    });
    
  } catch (error) {
    console.error('Error creating loan workflow:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create loan workflow',
      error: error.message
    });
  }
};

/**
 * Submit application to branch manager
 */
const submitToBranchManager = async (req, res) => {
  try {
    const { workflowId } = req.params;
    const { comments } = req.body;
    const userId = req.user.userId;
    
    const workflow = await LoanWorkflow.findById(workflowId).populate('branch');
    
    if (!workflow) {
      return res.status(404).json({
        success: false,
        message: 'Workflow not found'
      });
    }
    
    // Check if user can perform this action
    const actionCheck = workflow.canPerformAction(WORKFLOW_ACTIONS.SUBMIT_TO_BM, req.user.role, userId);
    if (!actionCheck.allowed) {
      return res.status(403).json({
        success: false,
        message: actionCheck.reason
      });
    }
    
    // Find branch manager for this branch
    const branchManager = await User.findOne({
      branch: workflow.branch._id,
      role: 'branch_manager',
      isActive: true
    });
    
    if (!branchManager) {
      return res.status(400).json({
        success: false,
        message: 'No active branch manager found for this branch'
      });
    }
    
    // Update workflow
    const fromStatus = workflow.currentStatus;
    workflow.currentStatus = WORKFLOW_STATUS.SUBMITTED_TO_BM;
    workflow.currentReviewer = branchManager._id;
    
    workflow.workflowHistory.push({
      action: WORKFLOW_ACTIONS.SUBMIT_TO_BM,
      fromStatus,
      toStatus: WORKFLOW_STATUS.SUBMITTED_TO_BM,
      performedBy: userId,
      performedByRole: req.user.role,
      loanAmount: workflow.loanAmount,
      comments: comments || 'Application submitted to branch manager'
    });
    
    await workflow.save();
    
    res.json({
      success: true,
      message: 'Application submitted to branch manager successfully',
      data: {
        currentStatus: workflow.currentStatus,
        currentReviewer: branchManager.firstName + ' ' + branchManager.lastName,
        nextActions: workflow.getAvailableActions('branch_manager', branchManager._id)
      }
    });
    
  } catch (error) {
    console.error('Error submitting to branch manager:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to submit application',
      error: error.message
    });
  }
};

/**
 * Branch manager approval/rejection/forwarding
 */
const branchManagerAction = async (req, res) => {
  try {
    const { workflowId } = req.params;
    const { action, comments } = req.body;
    const userId = req.user.userId;
    
    const workflow = await LoanWorkflow.findById(workflowId);
    
    if (!workflow) {
      return res.status(404).json({
        success: false,
        message: 'Workflow not found'
      });
    }
    
    // Validate action
    const validActions = [WORKFLOW_ACTIONS.APPROVE_BY_BM, WORKFLOW_ACTIONS.FORWARD_TO_GM, 
                         WORKFLOW_ACTIONS.REJECT, WORKFLOW_ACTIONS.RETURN_FOR_CHANGES];
    
    if (!validActions.includes(action)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid action'
      });
    }
    
    // Check if user can perform this action
    const actionCheck = workflow.canPerformAction(action, req.user.role, userId);
    if (!actionCheck.allowed) {
      return res.status(403).json({
        success: false,
        message: actionCheck.reason
      });
    }
    
    // For approval, check loan amount threshold
    if (action === WORKFLOW_ACTIONS.APPROVE_BY_BM && workflow.shouldForwardToGM()) {
      return res.status(400).json({
        success: false,
        message: `Loan amount ₹${workflow.loanAmount.toLocaleString()} exceeds ₹${(LOAN_AMOUNT_THRESHOLD).toLocaleString()} limit. Must forward to General Manager.`,
        suggestedAction: WORKFLOW_ACTIONS.FORWARD_TO_GM
      });
    }
    
    // Handle forwarding to GM
    let nextReviewer = null;
    if (action === WORKFLOW_ACTIONS.FORWARD_TO_GM) {
      const generalManager = await User.findOne({
        role: 'general_manager',
        isActive: true
      });
      
      if (!generalManager) {
        return res.status(400).json({
          success: false,
          message: 'No active general manager found'
        });
      }
      
      nextReviewer = generalManager._id;
    }
    
    // Update workflow
    const fromStatus = workflow.currentStatus;
    workflow.currentStatus = workflow.getNextStatus(action);
    workflow.currentReviewer = nextReviewer;
    
    if (action === WORKFLOW_ACTIONS.REJECT || action === WORKFLOW_ACTIONS.RETURN_FOR_CHANGES) {
      workflow.rejectionReason = comments;
    }
    
    workflow.workflowHistory.push({
      action,
      fromStatus,
      toStatus: workflow.currentStatus,
      performedBy: userId,
      performedByRole: req.user.role,
      loanAmount: workflow.loanAmount,
      comments: comments || `Action: ${action}`
    });
    
    await workflow.save();
    
    // Prepare response message
    let message = '';
    switch (action) {
      case WORKFLOW_ACTIONS.APPROVE_BY_BM:
        message = 'Application approved by branch manager';
        break;
      case WORKFLOW_ACTIONS.FORWARD_TO_GM:
        message = 'Application forwarded to general manager';
        break;
      case WORKFLOW_ACTIONS.REJECT:
        message = 'Application rejected';
        break;
      case WORKFLOW_ACTIONS.RETURN_FOR_CHANGES:
        message = 'Application returned for changes';
        break;
    }
    
    res.json({
      success: true,
      message,
      data: {
        currentStatus: workflow.currentStatus,
        currentReviewer: nextReviewer ? 'General Manager' : null,
        loanAmount: workflow.loanAmount,
        isAboveThreshold: workflow.shouldForwardToGM()
      }
    });
    
  } catch (error) {
    console.error('Error in branch manager action:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to process action',
      error: error.message
    });
  }
};

/**
 * General manager approval/rejection
 */
const generalManagerAction = async (req, res) => {
  try {
    const { workflowId } = req.params;
    const { action, comments } = req.body;
    const userId = req.user.userId;
    
    const workflow = await LoanWorkflow.findById(workflowId);
    
    if (!workflow) {
      return res.status(404).json({
        success: false,
        message: 'Workflow not found'
      });
    }
    
    // Validate action
    const validActions = [WORKFLOW_ACTIONS.APPROVE_BY_GM, WORKFLOW_ACTIONS.REJECT, WORKFLOW_ACTIONS.RETURN_FOR_CHANGES];
    
    if (!validActions.includes(action)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid action'
      });
    }
    
    // Check if user can perform this action
    const actionCheck = workflow.canPerformAction(action, req.user.role, userId);
    if (!actionCheck.allowed) {
      return res.status(403).json({
        success: false,
        message: actionCheck.reason
      });
    }
    
    // Update workflow
    const fromStatus = workflow.currentStatus;
    workflow.currentStatus = workflow.getNextStatus(action);
    workflow.currentReviewer = null; // No further reviewer needed
    
    if (action === WORKFLOW_ACTIONS.REJECT || action === WORKFLOW_ACTIONS.RETURN_FOR_CHANGES) {
      workflow.rejectionReason = comments;
    }
    
    workflow.workflowHistory.push({
      action,
      fromStatus,
      toStatus: workflow.currentStatus,
      performedBy: userId,
      performedByRole: req.user.role,
      loanAmount: workflow.loanAmount,
      comments: comments || `Action: ${action}`
    });
    
    await workflow.save();
    
    let message = '';
    switch (action) {
      case WORKFLOW_ACTIONS.APPROVE_BY_GM:
        message = 'Application approved by general manager';
        break;
      case WORKFLOW_ACTIONS.REJECT:
        message = 'Application rejected by general manager';
        break;
      case WORKFLOW_ACTIONS.RETURN_FOR_CHANGES:
        message = 'Application returned for changes by general manager';
        break;
    }
    
    res.json({
      success: true,
      message,
      data: {
        currentStatus: workflow.currentStatus,
        loanAmount: workflow.loanAmount,
        finalDecision: action === WORKFLOW_ACTIONS.APPROVE_BY_GM ? 'APPROVED' : 
                      action === WORKFLOW_ACTIONS.REJECT ? 'REJECTED' : 'RETURNED'
      }
    });
    
  } catch (error) {
    console.error('Error in general manager action:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to process action',
      error: error.message
    });
  }
};

/**
 * Get workflow status and available actions for frontend
 */
const getWorkflowStatus = async (req, res) => {
  try {
    const { workflowId } = req.params;
    const userId = req.user.userId;
    const userRole = req.user.role;

    const workflow = await LoanWorkflow.findById(workflowId)
      .populate('currentReviewer', 'firstName lastName email role')
      .populate('createdBy', 'firstName lastName email')
      .populate('branch', 'name code');

    if (!workflow) {
      return res.status(404).json({
        success: false,
        message: 'Workflow not found'
      });
    }

    // Generate complete frontend response
    const frontendData = generateFrontendResponse(workflow, userRole, userId, true);

    res.json({
      success: true,
      data: frontendData
    });

  } catch (error) {
    console.error('Error getting workflow status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get workflow status',
      error: error.message
    });
  }
};

/**
 * Get all workflows for current user based on role
 */
const getUserWorkflows = async (req, res) => {
  try {
    const userId = req.user.userId;
    const userRole = req.user.role;
    const { status, page = 1, limit = 10 } = req.query;

    let query = {};

    // Filter based on user role
    switch (userRole) {
      case 'deputy_manager':
        query.createdBy = userId;
        break;
      case 'branch_manager':
        query.$or = [
          { currentReviewer: userId },
          { currentStatus: WORKFLOW_STATUS.SUBMITTED_TO_BM }
        ];
        break;
      case 'general_manager':
        query.$or = [
          { currentReviewer: userId },
          { currentStatus: WORKFLOW_STATUS.FORWARDED_TO_GM }
        ];
        break;
      case 'super_admin':
        // Super admin can see all workflows
        break;
      default:
        return res.status(403).json({
          success: false,
          message: 'Unauthorized access'
        });
    }

    // Add status filter if provided
    if (status) {
      query.currentStatus = status;
    }

    const skip = (page - 1) * limit;

    const workflows = await LoanWorkflow.find(query)
      .populate('currentReviewer', 'firstName lastName email role')
      .populate('createdBy', 'firstName lastName email')
      .populate('branch', 'name code')
      .sort({ updatedAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    const total = await LoanWorkflow.countDocuments(query);

    // Add available actions for each workflow
    const workflowsWithActions = workflows.map(workflow => ({
      ...workflow.toObject(),
      availableActions: workflow.getAvailableActions(userRole, userId),
      isAboveThreshold: workflow.shouldForwardToGM()
    }));

    res.json({
      success: true,
      data: {
        workflows: workflowsWithActions,
        pagination: {
          current: parseInt(page),
          total: Math.ceil(total / limit),
          count: workflows.length,
          totalRecords: total
        }
      }
    });

  } catch (error) {
    console.error('Error getting user workflows:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get workflows',
      error: error.message
    });
  }
};

/**
 * Get workflow constants for frontend
 */
const getWorkflowConstants = (req, res) => {
  try {
    const constants = LoanWorkflow.getConstants();

    res.json({
      success: true,
      data: {
        statuses: constants.WORKFLOW_STATUS,
        actions: constants.WORKFLOW_ACTIONS,
        threshold: constants.LOAN_AMOUNT_THRESHOLD,
        transitions: constants.WORKFLOW_TRANSITIONS
      }
    });

  } catch (error) {
    console.error('Error getting workflow constants:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get workflow constants',
      error: error.message
    });
  }
};

module.exports = {
  createLoanWorkflow,
  submitToBranchManager,
  branchManagerAction,
  generalManagerAction,
  getWorkflowStatus,
  getUserWorkflows,
  getWorkflowConstants
};
