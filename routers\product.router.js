const express = require('express');
const router = express.Router();
const { check } = require('express-validator');
const auth = require('../middleware/auth.middleware');
const roleCheck = require('../middleware/role.middleware');
const {
  createProduct,
  getProduct,
  getProductById,
  updateProduct,
  deleteProduct,
  getProductsByLoanType,
} = require('../controllers/product.controller');

// Create a new product (Super Admin only)
router.post(
  '/',
  [
    auth,
    roleCheck(['superadmin']),
    [
      check('loanTypeId', 'Loan Type ID is required').not().isEmpty(),
      check('productCode', 'Product code is required').not().isEmpty(),
      check('minAmount', 'Minimum amount is required').not().isEmpty(),
      check('maxAmount', 'Maximum amount is required').not().isEmpty(),
      check('timePeriod', 'Time period is required').not().isEmpty(),
      check('interestRate', 'Interest rate is required').not().isEmpty(),
      check('isJoint', 'Joint status is required').isBoolean(),
    ],
  ],
  createProduct
);

/**
 * @swagger
 * /product:
 *   get:
 *     summary: Get all products with pagination
 *     tags: [Products]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: Products retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 docs:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Product'
 *                 total:
 *                   type: integer
 *                 limit:
 *                   type: integer
 *                 page:
 *                   type: integer
 *                 totalPages:
 *                   type: integer
 *                 hasNextPage:
 *                   type: boolean
 *                 hasPreviousPage:
 *                   type: boolean
 *                 nextPage:
 *                   type: integer
 *                   nullable: true
 *                 prevPage:
 *                   type: integer
 *                   nullable: true
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
// Get all products with pagination
router.get(
  '/',
  [auth],
  getProduct
);

/**
 * @swagger
 * /product/get:
 *   get:
 *     summary: Get all products with pagination (alternative route)
 *     tags: [Products]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: Products retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 docs:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Product'
 *                 total:
 *                   type: integer
 *                 limit:
 *                   type: integer
 *                 page:
 *                   type: integer
 *                 totalPages:
 *                   type: integer
 *                 hasNextPage:
 *                   type: boolean
 *                 hasPreviousPage:
 *                   type: boolean
 *                 nextPage:
 *                   type: integer
 *                   nullable: true
 *                 prevPage:
 *                   type: integer
 *                   nullable: true
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
// Alternative route for getting products (to match frontend call)
router.get(
  '/get',
  [auth],
  getProduct
);

// Get product by ID
router.get(
  '/:id',
  [auth],
  getProductById
);

// Update product (Super Admin only)
router.put(
  '/:id',
  [
    auth,
    roleCheck(['superadmin']),
    [
      check('productStatus', 'Valid status is required').optional().isIn(['active', 'inactive']),
    ],
  ],
  updateProduct
);

// Delete product (Soft delete, Super Admin only)
router.delete(
  '/:id',
  [auth, roleCheck(['superadmin'])],
  deleteProduct
);

/**
 * @swagger
 * /product/loan-type/{loanTypeId}:
 *   get:
 *     summary: Get products by loan type with pagination
 *     tags: [Products]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: loanTypeId
 *         required: true
 *         schema:
 *           type: string
 *         description: MongoDB ObjectId of the loan type
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: Products retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     docs:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Product'
 *                     total:
 *                       type: integer
 *                     limit:
 *                       type: integer
 *                     page:
 *                       type: integer
 *                     totalPages:
 *                       type: integer
 *                     hasNextPage:
 *                       type: boolean
 *                     hasPreviousPage:
 *                       type: boolean
 *                     nextPage:
 *                       type: integer
 *                       nullable: true
 *                     prevPage:
 *                       type: integer
 *                       nullable: true
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Loan type not found
 *       500:
 *         description: Internal server error
 */
// Get products by loan type
router.get(
  '/loan-type/:loanTypeId',
  [auth],
  getProductsByLoanType
);

module.exports = router;
