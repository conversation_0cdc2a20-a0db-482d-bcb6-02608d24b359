const express = require('express');
const router = express.Router();
const { check } = require('express-validator');
const auth = require('../middleware/auth.middleware');
const roleCheck = require('../middleware/role.middleware');
const {
  createProduct,
  getProduct,
  getProductById,
  updateProduct,
  deleteProduct,
  getProductsByLoanType,
} = require('../controllers/product.controller');

// Create a new product (Super Admin only)
router.post(
  '/',
  [
    auth,
    roleCheck(['superadmin']),
    [
      check('loanTypeId', 'Loan Type ID is required').not().isEmpty(),
      check('productCode', 'Product code is required').not().isEmpty(),
      check('minAmount', 'Minimum amount is required').not().isEmpty(),
      check('maxAmount', 'Maximum amount is required').not().isEmpty(),
      check('timePeriod', 'Time period is required').not().isEmpty(),
      check('interestRate', 'Interest rate is required').not().isEmpty(),
      check('isJoint', 'Joint status is required').isBoolean(),
    ],
  ],
  createProduct
);

// Get all products with pagination
router.get(
  '/',
  [auth],
  getProduct
);

// Alternative route for getting products (to match frontend call)
router.get(
  '/get',
  [auth],
  getProduct
);

// Get product by ID
router.get(
  '/:id',
  [auth],
  getProductById
);

// Update product (Super Admin only)
router.put(
  '/:id',
  [
    auth,
    roleCheck(['superadmin']),
    [
      check('productStatus', 'Valid status is required').optional().isIn(['active', 'inactive']),
    ],
  ],
  updateProduct
);

// Delete product (Soft delete, Super Admin only)
router.delete(
  '/:id',
  [auth, roleCheck(['superadmin'])],
  deleteProduct
);

// Get products by loan type
router.get(
  '/loan-type/:loanTypeId',
  [auth],
  getProductsByLoanType
);

module.exports = router;
