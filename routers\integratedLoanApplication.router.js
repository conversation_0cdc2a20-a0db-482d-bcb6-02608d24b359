const express = require('express');
const router = express.Router();
const { authMiddleware, roleMiddleware } = require('../middleware/auth.middleware');
const {
  createLoanApplication,
  updateLoanApplication,
  getLoanApplication,
  submitApplicationToBM
} = require('../controllers/integratedLoanApplication.controller');

/**
 * @swagger
 * components:
 *   schemas:
 *     IntegratedLoanApplication:
 *       type: object
 *       properties:
 *         workflowId:
 *           type: string
 *           description: Workflow ID
 *         applicationId:
 *           type: string
 *           description: Auto-generated application ID
 *         formDataId:
 *           type: string
 *           description: Form data ID
 *         loanType:
 *           type: string
 *           enum: [vehicle_loan, housing_loan, personal_loan, business_loan, education_loan, gold_loan]
 *         loanAmount:
 *           type: number
 *           description: Loan amount in rupees
 *         status:
 *           type: string
 *           description: Current workflow status
 *         formStatus:
 *           type: string
 *           description: Form data status
 *         ui:
 *           type: object
 *           description: Frontend UI state information
 *         formData:
 *           type: object
 *           description: Complete form data and metadata
 */

/**
 * @swagger
 * /applications:
 *   post:
 *     summary: Create a new loan application with form and workflow
 *     tags: [Integrated Loan Applications]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - loanType
 *               - formData
 *             properties:
 *               loanType:
 *                 type: string
 *                 enum: [vehicle_loan, housing_loan, personal_loan, business_loan, education_loan, gold_loan]
 *                 description: Type of loan application
 *               formData:
 *                 type: object
 *                 description: Form field values as key-value pairs
 *                 example:
 *                   firstName: "John"
 *                   lastName: "Doe"
 *                   email: "<EMAIL>"
 *                   phoneNumber: "9876543210"
 *                   loanAmount: 500000
 *                   vehicleType: "four_wheeler"
 *                   vehicleMake: "Maruti"
 *                   vehicleModel: "Swift"
 *               isDraft:
 *                 type: boolean
 *                 default: true
 *                 description: Whether to save as draft or submit for validation
 *     responses:
 *       201:
 *         description: Loan application created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   $ref: '#/components/schemas/IntegratedLoanApplication'
 *       400:
 *         description: Validation errors or missing required fields
 *       403:
 *         description: Only deputy managers can create applications
 *       404:
 *         description: Form schema not found for loan type
 */
router.post('/', 
  authMiddleware, 
  roleMiddleware(['deputy_manager']), 
  createLoanApplication
);

/**
 * @swagger
 * /applications/{workflowId}:
 *   get:
 *     summary: Get complete loan application details
 *     tags: [Integrated Loan Applications]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: workflowId
 *         required: true
 *         schema:
 *           type: string
 *         description: Workflow ID
 *     responses:
 *       200:
 *         description: Loan application retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   allOf:
 *                     - $ref: '#/components/schemas/IntegratedLoanApplication'
 *                     - type: object
 *                       properties:
 *                         workflow:
 *                           type: object
 *                           description: Complete workflow information
 *                         ui:
 *                           type: object
 *                           description: UI state with buttons, progress, notifications
 *                         formData:
 *                           type: object
 *                           description: Complete form data with history
 *       403:
 *         description: Access denied to this application
 *       404:
 *         description: Application not found
 */
router.get('/:workflowId', 
  authMiddleware, 
  roleMiddleware(['deputy_manager', 'branch_manager', 'general_manager', 'super_admin']), 
  getLoanApplication
);

/**
 * @swagger
 * /applications/{workflowId}:
 *   put:
 *     summary: Update existing loan application
 *     tags: [Integrated Loan Applications]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: workflowId
 *         required: true
 *         schema:
 *           type: string
 *         description: Workflow ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - formData
 *             properties:
 *               formData:
 *                 type: object
 *                 description: Updated form field values
 *               isDraft:
 *                 type: boolean
 *                 default: true
 *                 description: Whether to save as draft or submit for validation
 *     responses:
 *       200:
 *         description: Application updated successfully
 *       400:
 *         description: Validation errors or application cannot be edited
 *       403:
 *         description: Access denied or not owner of application
 *       404:
 *         description: Application not found
 */
router.put('/:workflowId', 
  authMiddleware, 
  roleMiddleware(['deputy_manager']), 
  updateLoanApplication
);

/**
 * @swagger
 * /applications/{workflowId}/submit:
 *   post:
 *     summary: Submit application to branch manager with PDF generation
 *     tags: [Integrated Loan Applications]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: workflowId
 *         required: true
 *         schema:
 *           type: string
 *         description: Workflow ID
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               comments:
 *                 type: string
 *                 description: Optional comments for submission
 *               generatePDF:
 *                 type: boolean
 *                 default: true
 *                 description: Whether to generate PDF before submission
 *     responses:
 *       200:
 *         description: Application submitted successfully
 *       400:
 *         description: Validation errors prevent submission
 *       403:
 *         description: Unauthorized or invalid action
 *       404:
 *         description: Application not found
 */
router.post('/:workflowId/submit', 
  authMiddleware, 
  roleMiddleware(['deputy_manager']), 
  submitApplicationToBM
);

/**
 * @swagger
 * /applications/{workflowId}/pdf:
 *   post:
 *     summary: Generate PDF for loan application
 *     tags: [Integrated Loan Applications]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: workflowId
 *         required: true
 *         schema:
 *           type: string
 *         description: Workflow ID
 *     responses:
 *       200:
 *         description: PDF generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     pdfUrl:
 *                       type: string
 *                     fileName:
 *                       type: string
 *       404:
 *         description: Application not found
 *       500:
 *         description: PDF generation failed
 */
router.post('/:workflowId/pdf', 
  authMiddleware, 
  roleMiddleware(['deputy_manager', 'branch_manager', 'general_manager', 'super_admin']), 
  async (req, res) => {
    try {
      const { workflowId } = req.params;
      const LoanFormData = require('../models/loanFormData.model');
      const pdfGeneratorService = require('../services/pdfGenerator.service');
      
      const formData = await LoanFormData.getByWorkflowId(workflowId);
      if (!formData) {
        return res.status(404).json({
          success: false,
          message: 'Application not found'
        });
      }
      
      const result = await pdfGeneratorService.generatePDF(formData._id);
      
      res.json({
        success: true,
        message: 'PDF generated successfully',
        data: {
          pdfUrl: result.pdfUrl,
          fileName: result.fileName
        }
      });
      
    } catch (error) {
      console.error('Error generating PDF:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to generate PDF',
        error: error.message
      });
    }
  }
);

/**
 * @swagger
 * /applications/{workflowId}/pdf/download:
 *   get:
 *     summary: Download PDF for loan application
 *     tags: [Integrated Loan Applications]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: workflowId
 *         required: true
 *         schema:
 *           type: string
 *         description: Workflow ID
 *     responses:
 *       200:
 *         description: PDF file
 *         content:
 *           application/pdf:
 *             schema:
 *               type: string
 *               format: binary
 *       404:
 *         description: PDF not found or not generated
 */
router.get('/:workflowId/pdf/download', 
  authMiddleware, 
  roleMiddleware(['deputy_manager', 'branch_manager', 'general_manager', 'super_admin']), 
  async (req, res) => {
    try {
      const { workflowId } = req.params;
      const LoanFormData = require('../models/loanFormData.model');
      
      const formData = await LoanFormData.getByWorkflowId(workflowId);
      if (!formData) {
        return res.status(404).json({
          success: false,
          message: 'Application not found'
        });
      }
      
      if (!formData.pdfStatus.isGenerated || !formData.pdfStatus.pdfPath) {
        return res.status(404).json({
          success: false,
          message: 'PDF not generated yet'
        });
      }
      
      const fs = require('fs');
      const path = require('path');
      
      if (!fs.existsSync(formData.pdfStatus.pdfPath)) {
        return res.status(404).json({
          success: false,
          message: 'PDF file not found'
        });
      }
      
      const fileName = path.basename(formData.pdfStatus.pdfPath);
      
      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);
      
      const fileStream = fs.createReadStream(formData.pdfStatus.pdfPath);
      fileStream.pipe(res);
      
    } catch (error) {
      console.error('Error downloading PDF:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to download PDF',
        error: error.message
      });
    }
  }
);

module.exports = router;
