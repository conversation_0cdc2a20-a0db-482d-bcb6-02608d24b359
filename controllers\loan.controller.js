const Loan = require('../models/loan.model');
const { ApiError } = require('../utils/errorHandler');
const { loanValidators } = require('../validators/loans');
const { ROLES, STATUS, ACTIONS } = require('../constants/roles');

/**
 * Create a new loan application
 */
const createLoan = async (req, res, next) => {
  try {
    const { loanType } = req.body.applicationInfo;
    
    // Create new loan application
    const loan = new Loan({
      ...req.body,
      status: 'draft', // Default status
      createdBy: req.user.id, // From auth middleware
      updatedBy: req.user.id
    });

    // Save to database
    await loan.save();

    // Return the created loan
    res.status(201).json({
      success: true,
      data: loan,
      message: 'Loan application created successfully'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Update an existing loan application
 */
const updateLoan = async (req, res, next) => {
  try {
    const { id } = req.params;
    const updateData = {
      ...req.body,
      updatedBy: req.user.id,
      updatedAt: Date.now()
    };

    // Find and update the loan
    const loan = await Loan.findByIdAndUpdate(
      id,
      { $set: updateData },
      { new: true, runValidators: true }
    );

    if (!loan) {
      throw new ApiError(404, 'Loan application not found');
    }

    // Check if user has permission to update this loan
    if (loan.createdBy.toString() !== req.user.id && !req.user.roles.includes('admin')) {
      throw new ApiError(403, 'Not authorized to update this loan application');
    }

    res.json({
      success: true,
      data: loan,
      message: 'Loan application updated successfully'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get loan by ID
 */
const getLoanById = async (req, res, next) => {
  try {
    const loan = await Loan.findById(req.params.id)
      .populate('createdBy', 'name email')
      .populate('updatedBy', 'name email');

    if (!loan) {
      throw new ApiError(404, 'Loan application not found');
    }

    // Check if user has permission to view this loan
    if (loan.createdBy._id.toString() !== req.user.id && 
        !req.user.roles.includes('admin')) {
      throw new ApiError(403, 'Not authorized to view this loan application');
    }

    res.json({
      success: true,
      data: loan
    });
  } catch (error) {
    next(error);
  }
};

/**
 * List all loan applications with filtering and pagination
 */
const listLoans = async (req, res, next) => {
  try {
    const { page = 1, limit = 10, status, loanType } = req.query;
    const skip = (page - 1) * limit;
    
    // Build query
    const query = {};
    
    // Filter by status if provided
    if (status) {
      query.status = status;
    }
    
    // Filter by loan type if provided
    if (loanType) {
      query['applicationInfo.loanType'] = loanType;
    }
    
    // Regular users can only see their own loans
    if (!req.user.roles.includes('admin')) {
      query.$or = [
        { createdBy: req.user.id },
        { 'primaryApplicant.personalDetails.email': req.user.email },
        { 'coApplicants.personalDetails.email': req.user.email }
      ];
    }

    // Get paginated results
    const [loans, total] = await Promise.all([
      Loan.find(query)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(parseInt(limit))
        .populate('createdBy', 'name email'),
      Loan.countDocuments(query)
    ]);

    res.json({
      success: true,
      data: loans,
      pagination: {
        total,
        page: parseInt(page),
        pages: Math.ceil(total / limit),
        limit: parseInt(limit)
      }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Transition loan status based on user role and current status
 */
const transitionStatus = async (req, res, next) => {
  const session = await mongoose.startSession();
  session.startTransaction();
  
  try {
    const { id } = req.params;
    const { action, comments = '' } = req.body;
    const { role, id: userId, branch } = req.user;

    // Find the loan
    const loan = await Loan.findById(id).session(session);
    if (!loan) {
      throw new ApiError('Loan not found', 404);
    }

    // Check branch access for branch-level roles
    if ([ROLES.BRANCH_MANAGER, ROLES.DEPUTY_MANAGER].includes(role) && 
        loan.branch.toString() !== branch?.toString()) {
      throw new ApiError('Not authorized to modify this loan', 403);
    }

    // Determine next status based on action and role
    let nextStatus;
    const currentStatus = loan.status;
    
    switch (action) {
      case 'submit':
        if (![ROLES.DEPUTY_MANAGER, ROLES.BRANCH_MANAGER].includes(role)) {
          throw new ApiError('You do not have permission to submit loans', 403);
        }
        nextStatus = STATUS.PENDING_APPROVAL;
        break;
        
      case 'approve':
        if (![ROLES.BRANCH_MANAGER, ROLES.GENERAL_MANAGER, ROLES.SUPER_ADMIN].includes(role)) {
          throw new ApiError('You do not have permission to approve loans', 403);
        }
        nextStatus = STATUS.APPROVED;
        break;
        
      case 'reject':
        if (![ROLES.BRANCH_MANAGER, ROLES.GENERAL_MANAGER, ROLES.SUPER_ADMIN].includes(role)) {
          throw new ApiError('You do not have permission to reject loans', 403);
        }
        nextStatus = STATUS.REJECTED;
        break;
        
      case 'return':
        if (![ROLES.BRANCH_MANAGER, ROLES.GENERAL_MANAGER, ROLES.SUPER_ADMIN].includes(role)) {
          throw new ApiError('You do not have permission to return loans', 403);
        }
        nextStatus = STATUS.DRAFT;
        break;
        
      case 'disburse':
        if (![ROLES.GENERAL_MANAGER, ROLES.SUPER_ADMIN].includes(role)) {
          throw new ApiError('You do not have permission to disburse loans', 403);
        }
        if (loan.status !== STATUS.APPROVED) {
          throw new ApiError('Only approved loans can be disbursed', 400);
        }
        nextStatus = STATUS.DISBURSED;
        break;
        
      default:
        throw new ApiError('Invalid action', 400);
    }

    // Update loan status and workflow
    loan.status = nextStatus;
    loan.updatedBy = userId;
    
    // Initialize workflow array if it doesn't exist
    if (!loan.workflow) {
      loan.workflow = [];
    }
    
    // Add to workflow history
    loan.workflow.push({
      fromStatus: currentStatus,
      toStatus: nextStatus,
      action,
      performedBy: userId,
      comments,
      timestamp: new Date()
    });

    // Save the updated loan
    const updatedLoan = await loan.save({ session });
    
    await session.commitTransaction();
    session.endSession();

    res.json({
      success: true,
      message: `Loan ${action}d successfully`,
      data: updatedLoan
    });
  } catch (error) {
    await session.abortTransaction();
    session.endSession();
    next(error);
  }
};

module.exports = {
  createLoan,
  updateLoan,
  getLoanById,
  listLoans,
  transitionStatus
};
