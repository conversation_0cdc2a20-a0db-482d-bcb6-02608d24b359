const loanCategories = [
  {
    groupName: "Short Term Loans, CC OD & Bills Discounted",
    name: "Cash Credit Against Stock - SSI",
    loanCode: "CC_STOCK_SSI",
  },
  {
    groupName: "Short Term Loans, CC OD & Bills Discounted",
    name: "Cash Credit Against Stock - NON SSI",
    loanCode: "CC_STOCK_NONSSI",
  },
  {
    groupName: "Short Term Loans, CC OD & Bills Discounted",
    name: "Cash Credit Against Stock and Book Debts - SSI",
    loanCode: "CC_STOCK_BOOKDEBT_SSI",
  },
  {
    groupName: "Short Term Loans, CC OD & Bills Discounted",
    name: "Cash Credit Against Stock and Book Debts - NON SSI",
    loanCode: "CC_STOCK_BOOKDEBT_NONSSI",
  },
  {
    groupName: "Short Term Loans, CC OD & Bills Discounted",
    name: "Overdraft Real Estate",
    loanCode: "OD_REAL_ESTATE",
  },
  {
    groupName: "Short Term Loans, CC OD & Bills Discounted",
    name: "Current Overdraft - FDR",
    loanCode: "OD_FDR",
  },
  {
    groupName: "Short Term Loans, CC OD & Bills Discounted",
    name: "Overdraft against - LIC",
    loanCode: "OD_LIC",
  },
  {
    groupName: "Short Term Loans, CC OD & Bills Discounted",
    name: "Overdraft against - NSC/KVP",
    loanCode: "OD_NSC_KVP",
  },
  {
    groupName: "Short Term Loans, CC OD & Bills Discounted",
    name: "Overdraft against Property - Non SSI",
    loanCode: "OD_PROPERTY_NONSSI",
  },
  {
    groupName: "Short Term Loans, CC OD & Bills Discounted",
    name: "Overdraft against Mortgage",
    loanCode: "OD_MORTGAGE",
  },
  {
    groupName: "Short Term Loans, CC OD & Bills Discounted",
    name: "MSME - Overdraft",
    loanCode: "OD_MSME",
  },
  {
    groupName: "Short Term Loans, CC OD & Bills Discounted",
    name: "Staff Secured OD",
    loanCode: "OD_STAFF_SECURED",
  },
  {
    groupName: "Short Term Loans, CC OD & Bills Discounted",
    name: "Gold Loan",
    loanCode: "LOAN_GOLD",
  },
  {
    groupName: "Short Term Loans, CC OD & Bills Discounted",
    name: "Silver Jubilee OD",
    loanCode: "OD_SILVER_JUBILEE",
  },

  // Medium Term Loans
  { groupName: "Medium Term Loans", name: "Advances against NSC/KVP", loanCode: "ADV_NSC_KVP" },
  { groupName: "Medium Term Loans", name: "Loan against FDR individual", loanCode: "LOAN_FDR_INDIVIDUAL" },
  { groupName: "Medium Term Loans", name: "Vehicle Loan", loanCode: "LOAN_VEHICLE" },
  { groupName: "Medium Term Loans", name: "Atma Nirbhar Gujarat Scheme - 1", loanCode: "AN_GUJARAT_1" },
  { groupName: "Medium Term Loans", name: "Atma Nirbhar Gujarat Scheme - 2", loanCode: "AN_GUJARAT_2" },
  { groupName: "Medium Term Loans", name: "Personal Loans - Others", loanCode: "LOAN_PERSONAL_OTHER" },

  // Long Term Loans
  { groupName: "Long Term Loans", name: "Machinery Loan", loanCode: "LOAN_MACHINERY" },
  { groupName: "Long Term Loans", name: "COVID-19 Special Personal Loan", loanCode: "LOAN_COVID_PERSONAL" },
  { groupName: "Long Term Loans", name: "Building Loan", loanCode: "LOAN_BUILDING" },
  { groupName: "Long Term Loans", name: "Business Enterprise Loan", loanCode: "LOAN_BUSINESS_ENTERPRISE" },
  { groupName: "Long Term Loans", name: "Staff Building Loan", loanCode: "LOAN_STAFF_BUILDING" },
  { groupName: "Long Term Loans", name: "Staff Vehicle Loan", loanCode: "LOAN_STAFF_VEHICLE" },
  { groupName: "Long Term Loans", name: "Mortgage Loan", loanCode: "LOAN_MORTGAGE" },
  { groupName: "Long Term Loans", name: "Silver Jubilee Loan", loanCode: "LOAN_SILVER_JUBILEE" },
];

module.exports = loanCategories;
