const mongoose = require("mongoose");

/**
 * Soft deletes a document by ID for any Mongoose model.
 *
 * @param {Mongoose.Model} model - The Mongoose model.
 * @param {string} id - The ID of the document to soft-delete.
 * @returns {Promise<Object|null>} - The updated document or null if not found.
 */
async function softDeleteById(
  model,
  id,
  additionalUpdates = {},
  softDeleteField = "isDeleted"
) {
  try {
    const updateFields = {
      [softDeleteField]: true,
      ...additionalUpdates,
    };

    const query = {
      _id: id,
      [softDeleteField]: false,
    };

    const updatedDoc = await model.findOneAndUpdate(
      query,
      { $set: updateFields },
      { new: true, runValidators: true }
    );

    if (!updatedDoc) {
      throw new Error(`${model.modelName} not found or already deleted`);
    }

    return updatedDoc;
  } catch (error) {
    console.error(`Error soft-deleting ${model.modelName}:`, error);
    throw error;
  }
}

/**
 * Generic paginated fetch function for any Mongoose model.
 *
 * @param {Mongoose.Model} model - The Mongoose model.
 * @param {Object} options - Options for the query.
 * @param {Number} options.page - Current page number (default: 1).
 * @param {Number} options.limit - Number of documents per page (default: 10).
 * @param {Object} options.filter - MongoDB query filter (default: {}).
 * @param {Object} options.sort - Sort options (default: { createdAt: -1 }).
 * @returns {Promise<Object>} - Paginated result with meta.
 */
async function paginateModel(
  model,
  {
    page = 1,
    limit = 10,
    filter = {},
    sort = { createdAt: -1 },
    populate = null, // can be string, object, or array
  } = {}
) {
  try {
    const skip = (page - 1) * limit;

    let query = model.find(filter).skip(skip).limit(limit).sort(sort);

    if (populate) {
      query = query.populate(populate);
      // Mongoose supports passing string, object, or array to populate directly
    }

    const [data, total] = await Promise.all([
      query,
      model.countDocuments(filter),
    ]);

    console.log("data", data)

    return {
      data,
      currentPage: page,
      totalPages: Math.ceil(total / limit),
      totalItems: total,
    };
  } catch (error) {
    console.error(`Error paginating ${model.modelName}:`, error);
    throw new Error(`Failed to retrieve ${model.modelName}s`);
  }
}

/**
 * Check if an error is a Mongoose validation error
 * @param {Error} error - Error object to check
 * @returns {boolean} True if validation error, false otherwise
 */
function isMongooseValidationError(error) {
  return (
    error instanceof mongoose.Error.ValidationError ||
    error.name === "ValidationError"
  );
}

/**
 * Handle and format Mongoose validation errors for response
 * @param {Object} res - Express response object
 * @param {Error} error - Validation error object
 * @returns {Object} Express response with formatted error
 */
function handleMongooseValidationError(res, error) {
  const errors = Object.values(error.errors).map((err) => ({
    field: err.path,
    message: err.message,
  }));

  return res.status(400).json({
    success: false,
    message: "Validation Error",
    errors,
  });
}

/**
 * Combined validation error check and handler
 * @param {Object} res - Express response object
 * @param {Error} error - Error to check/handle
 * @returns {boolean} True if handled validation error, false otherwise
 */
function mongooseValidateError(res, error) {
  if (!isMongooseValidationError(error)) return false;
  handleMongooseValidationError(res, error);
  return true;
}

module.exports = {
  softDeleteById,
  isMongooseValidationError,
  handleMongooseValidationError,
  mongooseValidateError,
  paginateModel,
};
