/**
 * @swagger
 * components:
 *   schemas:
 *     Product:
 *       type: object
 *       required:
 *         - loanTypeId
 *         - productCode
 *         - minAmount
 *         - maxAmount
 *         - timePeriod
 *         - interestRate
 *         - isJoint
 *         - productStatus
 *       properties:
 *         _id:
 *           type: string
 *           description: MongoDB ObjectId
 *           example: "64f8a1b2c3d4e5f6a7b8c9d0"
 *         loanTypeId:
 *           oneOf:
 *             - type: string
 *               description: MongoDB ObjectId reference to LoanType
 *               example: "64f8a1b2c3d4e5f6a7b8c9d1"
 *             - type: object
 *               description: Populated LoanType object
 *               properties:
 *                 _id:
 *                   type: string
 *                   example: "64f8a1b2c3d4e5f6a7b8c9d1"
 *                 loanCode:
 *                   type: string
 *                   example: "VL"
 *                 loanName:
 *                   type: string
 *                   example: "Vehicle Loan"
 *         productCode:
 *           type: string
 *           description: Unique product code
 *           example: "LOAN_VEHICLE"
 *         minAmount:
 *           type: string
 *           description: Minimum loan amount
 *           example: "100000"
 *         maxAmount:
 *           type: string
 *           description: Maximum loan amount
 *           example: "20000000"
 *         timePeriod:
 *           type: string
 *           description: Loan tenure in years
 *           example: "10"
 *         interestRate:
 *           type: string
 *           description: Interest rate percentage
 *           example: "9"
 *         isJoint:
 *           type: boolean
 *           description: Whether joint application is allowed
 *           example: true
 *         productStatus:
 *           type: string
 *           enum: [active, inactive]
 *           description: Product status
 *           example: "active"
 *         isDeleted:
 *           type: boolean
 *           description: Soft delete flag
 *           default: false
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Creation timestamp
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Last update timestamp
 *
 *     Branch:
 *       type: object
 *       required:
 *         - name
 *         - code
 *         - email
 *         - phone
 *         - address
 *         - ifscCode
 *         - micrCode
 *       properties:
 *         _id:
 *           type: string
 *           description: MongoDB ObjectId
 *           example: "64f8a1b2c3d4e5f6a7b8c9d2"
 *         name:
 *           type: string
 *           description: Branch name
 *           maxLength: 100
 *           example: "Head Office"
 *         code:
 *           type: string
 *           description: Unique branch code
 *           maxLength: 10
 *           example: "HO"
 *         email:
 *           type: string
 *           format: email
 *           description: Branch email address
 *           example: "<EMAIL>"
 *         phone:
 *           type: string
 *           pattern: "^[0-9]{10,15}$"
 *           description: Branch phone number
 *           example: "1234567890"
 *         address:
 *           type: object
 *           required:
 *             - street
 *             - city
 *             - state
 *             - postalCode
 *             - country
 *           properties:
 *             street:
 *               type: string
 *               description: Street address
 *               example: "123 Main Street"
 *             city:
 *               type: string
 *               description: City name
 *               example: "Mumbai"
 *             state:
 *               type: string
 *               description: State name
 *               example: "Maharashtra"
 *             postalCode:
 *               type: string
 *               description: Postal/ZIP code
 *               example: "400001"
 *             country:
 *               type: string
 *               description: Country name
 *               default: "India"
 *               example: "India"
 *             landmark:
 *               type: string
 *               description: Nearby landmark (optional)
 *               example: "Near Central Station"
 *         ifscCode:
 *           type: string
 *           pattern: "^[A-Z]{4}0[A-Z0-9]{6}$"
 *           description: IFSC code
 *           example: "BANK0000001"
 *         micrCode:
 *           type: string
 *           pattern: "^[0-9]{9}$"
 *           description: MICR code
 *           example: "*********"
 *         branchManager:
 *           type: string
 *           description: MongoDB ObjectId reference to User
 *           example: "64f8a1b2c3d4e5f6a7b8c9d3"
 *         openingDate:
 *           type: string
 *           format: date-time
 *           description: Branch opening date
 *         isActive:
 *           type: boolean
 *           description: Branch active status
 *           default: true
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Creation timestamp
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Last update timestamp
 *         createdBy:
 *           type: string
 *           description: MongoDB ObjectId of user who created the branch
 *         updatedBy:
 *           type: string
 *           description: MongoDB ObjectId of user who last updated the branch
 *
 *     PaginationResponse:
 *       type: object
 *       properties:
 *         docs:
 *           type: array
 *           description: Array of documents for current page
 *         total:
 *           type: integer
 *           description: Total number of documents
 *           example: 25
 *         limit:
 *           type: integer
 *           description: Number of items per page
 *           example: 10
 *         page:
 *           type: integer
 *           description: Current page number
 *           example: 1
 *         totalPages:
 *           type: integer
 *           description: Total number of pages
 *           example: 3
 *         hasNextPage:
 *           type: boolean
 *           description: Whether next page exists
 *           example: true
 *         hasPreviousPage:
 *           type: boolean
 *           description: Whether previous page exists
 *           example: false
 *         nextPage:
 *           type: integer
 *           nullable: true
 *           description: Next page number (null if no next page)
 *           example: 2
 *         prevPage:
 *           type: integer
 *           nullable: true
 *           description: Previous page number (null if no previous page)
 *           example: null
 *
 *     Error:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *           example: false
 *         message:
 *           type: string
 *           description: Error message
 *           example: "Validation failed"
 *         error:
 *           type: string
 *           description: Detailed error information (development only)
 *           example: "Product code is required"
 *
 *     Success:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *           example: true
 *         message:
 *           type: string
 *           description: Success message
 *           example: "Operation completed successfully"
 *         data:
 *           type: object
 *           description: Response data
 *
 *   securitySchemes:
 *     bearerAuth:
 *       type: http
 *       scheme: bearer
 *       bearerFormat: JWT
 *       description: JWT token for authentication
 */

module.exports = {};
