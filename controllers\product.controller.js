const Product = require("../schema/product.model");
const LoanType = require("../schema/loanType");
const { paginateModel } = require("../Utilss/common");
const { softDeleteById } = require("../utils/index");

const createProduct = async (req, res) => {
  try {
    if (req.user.role === "super_admin") {
      const { loanTypeId, productCode } = req.body;

      if (!productCode) {
        return res.status(400).json({ message: "Product code is required." });
      }

      // Validate that productCode belongs to the selected loanType
      const loanType = await LoanType.findById(loanTypeId);
      if (!loanType) {
        return res.status(400).json({ message: "Invalid loan type selected." });
      }

      if (loanType.loanCode !== productCode) {
        return res.status(400).json({
          message: `Product code must match the loan type code: ${loanType.loanCode}`,
        });
      }

      const product = new Product(req.body);
      await product.save();

      res.json({ message: "Product created successfully", product });
    } else {
      res.status(403).json({ error: "Unauthorized" });
    }
  } catch (err) {
    if (err.code === 11000) {
      return res.status(400).json({ message: "Product code must be unique." });
    }
    console.error("Error creating product:", err);
    res.status(500).json({ error: "Server Error" });
  }
};

const deleteProduct = async (req, res) => {
  try {
    if (req.user.role === "super_admin") {
      const deletedProduct = await softDeleteById(Product, req.params.id, {
        product_status: "inactive",
      });

      if (!deletedProduct) {
        return res.status(404).json({ message: "Product not found" });
      }

      res.json({
        message: "Product soft-deleted successfully",
        deletedProduct,
      });
    } else {
      res.status(403).json({ error: "Unauthorized" });
    }
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: error.message });
  }
};

const getProduct = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;

    const result = await paginateModel(Product, {
      page,
      limit,
      filter: { isDeleted: false },
      populate: "loanTypeId",
    });

    // Transform the response to match expected format
    const response = {
      docs: result.data,
      total: result.totalItems,
      limit: limit,
      page: result.currentPage,
      totalPages: result.totalPages,
      hasNextPage: result.currentPage < result.totalPages,
      hasPreviousPage: result.currentPage > 1,
      nextPage: result.currentPage < result.totalPages ? result.currentPage + 1 : null,
      prevPage: result.currentPage > 1 ? result.currentPage - 1 : null
    };

    res.status(200).json(response);
  } catch (error) {
    console.error("Error getting products:", error.message);
    res.status(500).json({ success: false, message: "Internal server error" });
  }
};

const getProductById = async (req, res) => {
  try {
    const product = await Product.findOne({
      _id: req.params.id,
      isDeleted: false,
    }).populate("loanTypeId");

    if (!product) {
      return res.status(404).json({
        success: false,
        message: "Product not found",
      });
    }

    res.status(200).json({
      success: true,
      data: product,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message,
    });
  }
};

const updateProduct = async (req, res) => {
  try {
    const updates = Object.keys(req.body);
    const allowedUpdates = [
      "minAmount",
      "maxAmount",
      "timePeriod",
      "interestRate",
      "isJoint",
      "productStatus",
    ];
    const isValidOperation = updates.every((update) =>
      allowedUpdates.includes(update)
    );

    if (!isValidOperation) {
      return res.status(400).json({ error: "Invalid updates!" });
    }

    const product = await Product.findOne({
      _id: req.params.id,
      isDeleted: false,
    });

    if (!product) {
      return res.status(404).json({ message: "Product not found" });
    }

    updates.forEach((update) => (product[update] = req.body[update]));
    await product.save();

    res.json({ message: "Product updated successfully", product });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

const getProductsByLoanType = async (req, res) => {
  try {
    const { loanTypeId } = req.params;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;

    const result = await paginateModel(Product, {
      page,
      limit,
      filter: {
        loanTypeId,
        isDeleted: false,
        productStatus: "active",
      },
      populate: "loanTypeId",
    });

    // Transform the response to match expected format
    const response = {
      docs: result.data,
      total: result.totalItems,
      limit: limit,
      page: result.currentPage,
      totalPages: result.totalPages,
      hasNextPage: result.currentPage < result.totalPages,
      hasPreviousPage: result.currentPage > 1,
      nextPage: result.currentPage < result.totalPages ? result.currentPage + 1 : null,
      prevPage: result.currentPage > 1 ? result.currentPage - 1 : null
    };

    res.status(200).json({ success: true, data: response });
  } catch (error) {
    console.error("Error getting products by loan type:", error.message);
    res.status(500).json({ success: false, message: "Internal server error" });
  }
};

module.exports = {
  createProduct,
  deleteProduct,
  getProduct,
  getProductById,
  updateProduct,
  getProductsByLoanType,
};
