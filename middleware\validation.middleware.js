const { ValidationError } = require('../utils/errorHandler');
const logger = require('../templates/logger');

/**
 * Formats Joi validation errors into a more readable format
 * @param {Array} errorDetails - Array of Joi error details
 * @returns {Array} Formatted error objects
 */
const formatValidationErrors = (errorDetails) => {
    return errorDetails.map((detail) => {
        const path = detail.path.join('.');
        let message = detail.message.replace(/"/g, '');
        
        // Customize messages for specific validation types
        switch (detail.type) {
            case 'string.email':
                message = 'Please provide a valid email address';
                break;
            case 'string.pattern.base':
                if (detail.context.regex.toString().includes('password')) {
                    message = 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character';
                } else if (detail.context.regex.toString().includes('phone')) {
                    message = 'Please provide a valid phone number';
                }
                break;
            case 'any.required':
                message = `${path} is required`;
                break;
            case 'string.min':
                message = `${path} must be at least ${detail.context.limit} characters long`;
                break;
            case 'string.max':
                message = `${path} cannot be longer than ${detail.context.limit} characters`;
                break;
            case 'date.format':
                message = `${path} must be a valid date in YYYY-MM-DD format`;
                break;
        }

        return {
            field: path,
            message,
            type: detail.type,
            context: detail.context
        };
    });
};

/**
 * Middleware to validate request data against a Joi schema
 * @param {Joi.Schema} schema - Joi validation schema
 * @param {string} [source='body'] - Request property to validate (body, params, query)
 * @param {Object} [options] - Additional validation options
 * @param {boolean} [options.allowUnknown=true] - Whether to allow unknown keys
 * @param {boolean} [options.abortEarly=false] - Whether to stop on first error
 * @returns {Function} Express middleware function
 */
const validate = (schema, source = 'body', options = {}) => {
    const defaultOptions = {
        abortEarly: false, // Return all validation errors
        allowUnknown: true, // Allow unknown keys that will be ignored
        stripUnknown: true, // Remove unknown elements
        ...options
    };

    return (req, res, next) => {
        try {
            const { error, value } = schema.validate(req[source], defaultOptions);

            if (error) {
                const formattedErrors = formatValidationErrors(error.details);
                
                // Log validation errors for debugging
                logger.warn('Validation failed', {
                    path: req.path,
                    method: req.method,
                    errors: formattedErrors,
                    body: req.body,
                    params: req.params,
                    query: req.query,
                    user: req.user ? req.user.id : 'unauthenticated'
                });

                return next(new ValidationError(formattedErrors));
            }

            // Replace the request data with the validated and sanitized data
            req[source] = value;
            next();
        } catch (error) {
            logger.error('Validation middleware error', {
                error: error.message,
                stack: error.stack,
                path: req.path,
                method: req.method
            });
            next(error);
        }
    };
};

module.exports = validate;
