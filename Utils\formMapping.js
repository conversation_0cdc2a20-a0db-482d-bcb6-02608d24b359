/**
 * Form Field Mapping Utilities
 * This file contains utilities for mapping form fields between different schemas
 * and ensuring consistency between frontend forms and primary application data
 */

// Standard field mappings for common loan application fields
const STANDARD_FIELD_MAPPINGS = {
  // Personal Information
  'firstName': 'personalInfo.fname',
  'middleName': 'personalInfo.mname',
  'lastName': 'personalInfo.lname',
  'email': 'personalInfo.email',
  'gender': 'personalInfo.gender',
  'motherName': 'personalInfo.motherName',
  'dateOfBirth': 'personalInfo.dateOfBirth',
  'maritalStatus': 'personalInfo.maritalStatus',
  
  // Contact Information
  'phoneNumber': 'phone.number',
  'phoneCode': 'phone.code',
  'alternatePhoneNumber': 'alternatePhone.number',
  'alternatePhoneCode': 'alternatePhone.code',
  
  // Address Information - Home
  'homeAddressLine1': 'homeAddress.line1',
  'homeAddressLine2': 'homeAddress.line2',
  'homeAddressLine3': 'homeAddress.line3',
  'homeCity': 'homeAddress.city',
  'homeState': 'homeAddress.state',
  'homePincode': 'homeAddress.pincode',
  'homeCountry': 'homeAddress.country',
  
  // Address Information - Business
  'businessAddressLine1': 'businessAddress.line1',
  'businessAddressLine2': 'businessAddress.line2',
  'businessAddressLine3': 'businessAddress.line3',
  'businessCity': 'businessAddress.city',
  'businessState': 'businessAddress.state',
  'businessPincode': 'businessAddress.pincode',
  'businessCountry': 'businessAddress.country',
  
  // Loan Information
  'loanAmount': 'loanInfo.amount',
  'loanPurpose': 'loanInfo.purpose',
  'loanTenure': 'loanInfo.tenure',
  'loanType': 'loanInfo.type',
  'interestRate': 'loanInfo.interestRate',
  
  // Occupation Information
  'occupationType': 'occupation.type',
  'companyName': 'occupation.companyName',
  'designation': 'occupation.designation',
  'workExperience': 'occupation.workExperience',
  'monthlyIncome': 'occupation.monthlyIncome',
  
  // Financial Information
  'annualIncome': 'financialInfo.annualIncome',
  'monthlyExpenses': 'financialInfo.monthlyExpenses',
  'existingLoans': 'financialInfo.existingLoans',
  'bankAccountNumber': 'financialInfo.bankAccountNumber',
  'bankName': 'financialInfo.bankName',
  'ifscCode': 'financialInfo.ifscCode'
};

// Loan type specific mappings
const LOAN_TYPE_MAPPINGS = {
  vehicle_loan: {
    'vehicleType': 'loanSpecific.vehicleType',
    'vehicleMake': 'loanSpecific.vehicleMake',
    'vehicleModel': 'loanSpecific.vehicleModel',
    'vehicleYear': 'loanSpecific.vehicleYear',
    'vehiclePrice': 'loanSpecific.vehiclePrice',
    'downPayment': 'loanSpecific.downPayment',
    'dealerName': 'loanSpecific.dealerName',
    'vehicleRegistrationNumber': 'loanSpecific.registrationNumber',
    'engineNumber': 'loanSpecific.engineNumber',
    'chassisNumber': 'loanSpecific.chassisNumber'
  },
  
  housing_loan: {
    'propertyType': 'loanSpecific.propertyType',
    'propertyValue': 'loanSpecific.propertyValue',
    'propertyAddress': 'loanSpecific.propertyAddress',
    'constructionStatus': 'loanSpecific.constructionStatus',
    'builderName': 'loanSpecific.builderName',
    'projectName': 'loanSpecific.projectName',
    'carpetArea': 'loanSpecific.carpetArea',
    'stampDutyPaid': 'loanSpecific.stampDutyPaid',
    'registrationCharges': 'loanSpecific.registrationCharges'
  },
  
  personal_loan: {
    'loanPurposeDetails': 'loanSpecific.purposeDetails',
    'repaymentMode': 'loanSpecific.repaymentMode',
    'preferredEMIDate': 'loanSpecific.preferredEMIDate'
  },
  
  business_loan: {
    'businessType': 'loanSpecific.businessType',
    'businessName': 'loanSpecific.businessName',
    'businessRegistrationNumber': 'loanSpecific.registrationNumber',
    'businessEstablishedYear': 'loanSpecific.establishedYear',
    'businessTurnover': 'loanSpecific.annualTurnover',
    'businessProfit': 'loanSpecific.annualProfit',
    'gstNumber': 'loanSpecific.gstNumber',
    'panNumber': 'loanSpecific.panNumber'
  },
  
  education_loan: {
    'courseName': 'loanSpecific.courseName',
    'instituteName': 'loanSpecific.instituteName',
    'courseDuration': 'loanSpecific.courseDuration',
    'courseFee': 'loanSpecific.courseFee',
    'studentName': 'loanSpecific.studentName',
    'studentRelation': 'loanSpecific.studentRelation',
    'academicYear': 'loanSpecific.academicYear'
  },
  
  gold_loan: {
    'goldWeight': 'loanSpecific.goldWeight',
    'goldPurity': 'loanSpecific.goldPurity',
    'goldValue': 'loanSpecific.goldValue',
    'ornamentType': 'loanSpecific.ornamentType',
    'appraisalValue': 'loanSpecific.appraisalValue'
  }
};

// PDF template variable mappings
const PDF_TEMPLATE_MAPPINGS = {
  // Personal Information
  'firstName': 'applicant_first_name',
  'middleName': 'applicant_middle_name',
  'lastName': 'applicant_last_name',
  'email': 'applicant_email',
  'phoneNumber': 'applicant_phone',
  'dateOfBirth': 'applicant_dob',
  'gender': 'applicant_gender',
  'maritalStatus': 'applicant_marital_status',
  'motherName': 'applicant_mother_name',
  
  // Address Information
  'homeAddressLine1': 'home_address_line1',
  'homeAddressLine2': 'home_address_line2',
  'homeCity': 'home_city',
  'homeState': 'home_state',
  'homePincode': 'home_pincode',
  
  // Loan Information
  'loanAmount': 'loan_amount',
  'loanPurpose': 'loan_purpose',
  'loanTenure': 'loan_tenure',
  'interestRate': 'interest_rate',
  
  // Financial Information
  'monthlyIncome': 'monthly_income',
  'annualIncome': 'annual_income',
  'monthlyExpenses': 'monthly_expenses',
  
  // System Information
  'applicationDate': 'application_date',
  'applicationId': 'application_id',
  'branchName': 'branch_name',
  'branchCode': 'branch_code'
};

/**
 * Get field mapping for a specific loan type
 */
const getFieldMapping = (loanType, fieldName) => {
  // First check standard mappings
  if (STANDARD_FIELD_MAPPINGS[fieldName]) {
    return STANDARD_FIELD_MAPPINGS[fieldName];
  }
  
  // Then check loan type specific mappings
  if (LOAN_TYPE_MAPPINGS[loanType] && LOAN_TYPE_MAPPINGS[loanType][fieldName]) {
    return LOAN_TYPE_MAPPINGS[loanType][fieldName];
  }
  
  return null;
};

/**
 * Get PDF template variable name for a field
 */
const getPDFTemplateVariable = (fieldName) => {
  return PDF_TEMPLATE_MAPPINGS[fieldName] || fieldName.toLowerCase().replace(/([A-Z])/g, '_$1').toLowerCase();
};

/**
 * Generate complete field mapping for a loan type
 */
const generateCompleteMapping = (loanType) => {
  const mapping = { ...STANDARD_FIELD_MAPPINGS };
  
  if (LOAN_TYPE_MAPPINGS[loanType]) {
    Object.assign(mapping, LOAN_TYPE_MAPPINGS[loanType]);
  }
  
  return mapping;
};

/**
 * Validate field mapping consistency
 */
const validateFieldMapping = (formFields, loanType) => {
  const issues = [];
  const mapping = generateCompleteMapping(loanType);
  
  formFields.forEach(field => {
    const fieldName = field.fieldName;
    
    // Check if field has mapping
    if (!mapping[fieldName] && !field.primaryApplicationMapping) {
      issues.push({
        field: fieldName,
        issue: 'No mapping defined',
        severity: 'warning'
      });
    }
    
    // Check if PDF mapping exists
    if (!field.pdfMapping && !PDF_TEMPLATE_MAPPINGS[fieldName]) {
      issues.push({
        field: fieldName,
        issue: 'No PDF template mapping defined',
        severity: 'info'
      });
    }
  });
  
  return issues;
};

/**
 * Auto-generate field mappings for a form schema
 */
const autoGenerateFieldMappings = (formFields, loanType) => {
  const updatedFields = formFields.map(field => {
    const fieldName = field.fieldName;
    
    // Auto-assign primary application mapping if not exists
    if (!field.primaryApplicationMapping) {
      const mapping = getFieldMapping(loanType, fieldName);
      if (mapping) {
        field.primaryApplicationMapping = {
          fieldPath: mapping,
          transformer: null
        };
      }
    }
    
    // Auto-assign PDF mapping if not exists
    if (!field.pdfMapping) {
      field.pdfMapping = {
        templateVariable: getPDFTemplateVariable(fieldName),
        formatter: getDefaultFormatter(field.fieldType)
      };
    }
    
    return field;
  });
  
  return updatedFields;
};

/**
 * Get default formatter based on field type
 */
const getDefaultFormatter = (fieldType) => {
  const formatters = {
    'currency': 'currency',
    'percentage': 'percentage',
    'date': 'date',
    'phone': 'phone',
    'email': null,
    'text': null,
    'number': null
  };
  
  return formatters[fieldType] || null;
};

/**
 * Transform form data using field mappings
 */
const transformFormData = (formData, fieldMappings) => {
  const transformedData = {};
  
  Object.keys(formData).forEach(fieldName => {
    const mapping = fieldMappings.find(m => m.fieldName === fieldName);
    if (mapping && mapping.primaryApplicationMapping) {
      const targetPath = mapping.primaryApplicationMapping.fieldPath;
      const value = formData[fieldName];
      
      // Apply transformer if specified
      let transformedValue = value;
      if (mapping.primaryApplicationMapping.transformer) {
        transformedValue = applyTransformer(mapping.primaryApplicationMapping.transformer, value);
      }
      
      setNestedProperty(transformedData, targetPath, transformedValue);
    }
  });
  
  return transformedData;
};

/**
 * Apply data transformer
 */
const applyTransformer = (transformerName, value) => {
  const transformers = {
    'toNumber': (val) => Number(val),
    'toString': (val) => String(val),
    'toUpperCase': (val) => String(val).toUpperCase(),
    'toLowerCase': (val) => String(val).toLowerCase(),
    'toDate': (val) => new Date(val),
    'removeSpaces': (val) => String(val).replace(/\s/g, ''),
    'formatPhone': (val) => String(val).replace(/\D/g, '').slice(-10)
  };
  
  return transformers[transformerName] ? transformers[transformerName](value) : value;
};

/**
 * Set nested property using dot notation
 */
const setNestedProperty = (obj, path, value) => {
  const keys = path.split('.');
  let current = obj;
  
  for (let i = 0; i < keys.length - 1; i++) {
    const key = keys[i];
    if (!(key in current)) {
      current[key] = {};
    }
    current = current[key];
  }
  
  current[keys[keys.length - 1]] = value;
};

module.exports = {
  STANDARD_FIELD_MAPPINGS,
  LOAN_TYPE_MAPPINGS,
  PDF_TEMPLATE_MAPPINGS,
  getFieldMapping,
  getPDFTemplateVariable,
  generateCompleteMapping,
  validateFieldMapping,
  autoGenerateFieldMappings,
  transformFormData,
  applyTransformer,
  setNestedProperty
};
