const express = require('express');
const router = express.Router();
const { authMiddleware, roleMiddleware } = require('../middleware/auth.middleware');
const {
  createLoanWorkflow,
  submitToBranchManager,
  branchManagerAction,
  generalManagerAction,
  getWorkflowStatus,
  getUserWorkflows,
  getWorkflowConstants
} = require('../controllers/loanWorkflow.controller');

/**
 * @swagger
 * components:
 *   schemas:
 *     LoanWorkflow:
 *       type: object
 *       properties:
 *         _id:
 *           type: string
 *           description: Workflow ID
 *         applicationId:
 *           type: string
 *           description: Auto-generated application ID
 *         currentStatus:
 *           type: string
 *           enum: [draft, submitted_to_branch_manager, branch_manager_review, approved_by_branch_manager, forwarded_to_general_manager, general_manager_review, approved_by_general_manager, rejected, returned_for_changes]
 *         loanAmount:
 *           type: number
 *           description: Loan amount in rupees
 *         currentReviewer:
 *           type: string
 *           description: ID of current reviewer
 *         createdBy:
 *           type: string
 *           description: ID of application creator
 *         branch:
 *           type: string
 *           description: Branch ID
 *         workflowHistory:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               action:
 *                 type: string
 *               fromStatus:
 *                 type: string
 *               toStatus:
 *                 type: string
 *               performedBy:
 *                 type: string
 *               performedByRole:
 *                 type: string
 *               comments:
 *                 type: string
 *               timestamp:
 *                 type: string
 *                 format: date-time
 */

/**
 * @swagger
 * /loan-workflow/constants:
 *   get:
 *     summary: Get workflow constants for frontend
 *     tags: [Loan Workflow]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Workflow constants retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     statuses:
 *                       type: object
 *                     actions:
 *                       type: object
 *                     threshold:
 *                       type: number
 *                     transitions:
 *                       type: object
 */
router.get('/constants', authMiddleware, getWorkflowConstants);

/**
 * @swagger
 * /loan-workflow:
 *   post:
 *     summary: Create a new loan workflow (Deputy Manager only)
 *     tags: [Loan Workflow]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - applicationData
 *               - loanAmount
 *             properties:
 *               applicationData:
 *                 type: string
 *                 description: Reference to PrimaryApplication ID
 *               loanAmount:
 *                 type: number
 *                 description: Loan amount in rupees
 *               branchId:
 *                 type: string
 *                 description: Branch ID (optional, defaults to user's branch)
 *     responses:
 *       201:
 *         description: Workflow created successfully
 *       403:
 *         description: Only deputy managers can create workflows
 */
router.post('/', 
  authMiddleware, 
  roleMiddleware(['deputy_manager']), 
  createLoanWorkflow
);

/**
 * @swagger
 * /loan-workflow:
 *   get:
 *     summary: Get workflows for current user
 *     tags: [Loan Workflow]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *         description: Filter by workflow status
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Items per page
 *     responses:
 *       200:
 *         description: Workflows retrieved successfully
 */
router.get('/', 
  authMiddleware, 
  roleMiddleware(['deputy_manager', 'branch_manager', 'general_manager', 'super_admin']), 
  getUserWorkflows
);

/**
 * @swagger
 * /loan-workflow/{workflowId}/status:
 *   get:
 *     summary: Get workflow status and available actions
 *     tags: [Loan Workflow]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: workflowId
 *         required: true
 *         schema:
 *           type: string
 *         description: Workflow ID
 *     responses:
 *       200:
 *         description: Workflow status retrieved successfully
 *       404:
 *         description: Workflow not found
 */
router.get('/:workflowId/status', 
  authMiddleware, 
  roleMiddleware(['deputy_manager', 'branch_manager', 'general_manager', 'super_admin']), 
  getWorkflowStatus
);

/**
 * @swagger
 * /loan-workflow/{workflowId}/submit-to-bm:
 *   post:
 *     summary: Submit application to branch manager (Deputy Manager only)
 *     tags: [Loan Workflow]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: workflowId
 *         required: true
 *         schema:
 *           type: string
 *         description: Workflow ID
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               comments:
 *                 type: string
 *                 description: Optional comments
 *     responses:
 *       200:
 *         description: Application submitted successfully
 *       403:
 *         description: Unauthorized or invalid action
 *       404:
 *         description: Workflow not found
 */
router.post('/:workflowId/submit-to-bm', 
  authMiddleware, 
  roleMiddleware(['deputy_manager']), 
  submitToBranchManager
);

/**
 * @swagger
 * /loan-workflow/{workflowId}/branch-manager-action:
 *   post:
 *     summary: Branch manager actions (approve/reject/forward/return)
 *     tags: [Loan Workflow]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: workflowId
 *         required: true
 *         schema:
 *           type: string
 *         description: Workflow ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - action
 *             properties:
 *               action:
 *                 type: string
 *                 enum: [approve_by_branch_manager, forward_to_general_manager, reject, return_for_changes]
 *                 description: Action to perform
 *               comments:
 *                 type: string
 *                 description: Comments (required for reject/return)
 *     responses:
 *       200:
 *         description: Action performed successfully
 *       400:
 *         description: Invalid action or loan amount exceeds threshold
 *       403:
 *         description: Unauthorized
 *       404:
 *         description: Workflow not found
 */
router.post('/:workflowId/branch-manager-action', 
  authMiddleware, 
  roleMiddleware(['branch_manager', 'super_admin']), 
  branchManagerAction
);

/**
 * @swagger
 * /loan-workflow/{workflowId}/general-manager-action:
 *   post:
 *     summary: General manager actions (approve/reject/return)
 *     tags: [Loan Workflow]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: workflowId
 *         required: true
 *         schema:
 *           type: string
 *         description: Workflow ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - action
 *             properties:
 *               action:
 *                 type: string
 *                 enum: [approve_by_general_manager, reject, return_for_changes]
 *                 description: Action to perform
 *               comments:
 *                 type: string
 *                 description: Comments (required for reject/return)
 *     responses:
 *       200:
 *         description: Action performed successfully
 *       400:
 *         description: Invalid action
 *       403:
 *         description: Unauthorized
 *       404:
 *         description: Workflow not found
 */
router.post('/:workflowId/general-manager-action', 
  authMiddleware, 
  roleMiddleware(['general_manager', 'super_admin']), 
  generalManagerAction
);

module.exports = router;
