const puppeteer = require('puppeteer');
const handlebars = require('handlebars');
const fs = require('fs').promises;
const path = require('path');
const LoanFormData = require('../models/loanFormData.model');

// Register Handlebars helpers
handlebars.registerHelper('formatCurrency', function(amount) {
  if (!amount) return '₹0';
  return `₹${Number(amount).toLocaleString('en-IN')}`;
});

handlebars.registerHelper('formatDate', function(date) {
  if (!date) return '';
  return new Date(date).toLocaleDateString('en-IN');
});

handlebars.registerHelper('formatPhone', function(phone) {
  if (!phone) return '';
  const phoneStr = phone.toString();
  return phoneStr.replace(/(\d{3})(\d{3})(\d{4})/, '$1-$2-$3');
});

handlebars.registerHelper('uppercase', function(str) {
  return str ? str.toString().toUpperCase() : '';
});

handlebars.registerHelper('capitalize', function(str) {
  if (!str) return '';
  return str.toString().charAt(0).toUpperCase() + str.toString().slice(1);
});

handlebars.registerHelper('eq', function(a, b) {
  return a === b;
});

handlebars.registerHelper('gt', function(a, b) {
  return a > b;
});

handlebars.registerHelper('lt', function(a, b) {
  return a < b;
});

class PDFGeneratorService {
  constructor() {
    this.browser = null;
    this.templatesDir = path.join(__dirname, '../templates/pdf');
    this.outputDir = path.join(__dirname, '../uploads/pdfs');
    this.ensureDirectories();
  }

  async ensureDirectories() {
    try {
      await fs.mkdir(this.templatesDir, { recursive: true });
      await fs.mkdir(this.outputDir, { recursive: true });
    } catch (error) {
      console.error('Error creating directories:', error);
    }
  }

  async initBrowser() {
    if (!this.browser) {
      this.browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox']
      });
    }
    return this.browser;
  }

  async closeBrowser() {
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
    }
  }

  /**
   * Generate PDF from form data
   */
  async generatePDF(formDataId, options = {}) {
    try {
      // Get form data with schema
      const formData = await LoanFormData.findById(formDataId)
        .populate('formSchema')
        .populate('submittedBy', 'firstName lastName email')
        .populate('branch', 'name code address');

      if (!formData) {
        throw new Error('Form data not found');
      }

      // Generate PDF data for template
      const pdfData = await formData.generatePDFData();
      
      // Add additional metadata
      pdfData.submittedBy = formData.submittedBy;
      pdfData.branch = formData.branch;
      pdfData.submissionDate = formData.createdAt;
      pdfData.applicationId = formData.workflowId;

      // Get template configuration
      const templateConfig = formData.formSchema.pdfTemplate;
      const templatePath = path.join(this.templatesDir, `${formData.loanType}.hbs`);

      // Generate HTML from template
      const html = await this.generateHTML(templatePath, pdfData);

      // Generate PDF
      const pdfBuffer = await this.generatePDFFromHTML(html, {
        format: templateConfig.format || 'A4',
        orientation: templateConfig.orientation || 'portrait',
        margins: templateConfig.margins || {
          top: '20mm',
          right: '20mm',
          bottom: '20mm',
          left: '20mm'
        },
        ...options
      });

      // Save PDF file
      const fileName = `${formData.loanType}_${formData.workflowId}_${Date.now()}.pdf`;
      const filePath = path.join(this.outputDir, fileName);
      
      await fs.writeFile(filePath, pdfBuffer);

      // Update form data with PDF info
      formData.pdfStatus = {
        isGenerated: true,
        pdfPath: filePath,
        pdfUrl: `/uploads/pdfs/${fileName}`,
        generatedAt: new Date()
      };
      
      await formData.save();

      return {
        success: true,
        pdfPath: filePath,
        pdfUrl: `/uploads/pdfs/${fileName}`,
        fileName: fileName
      };

    } catch (error) {
      console.error('Error generating PDF:', error);
      
      // Update form data with error
      if (formDataId) {
        try {
          await LoanFormData.findByIdAndUpdate(formDataId, {
            'pdfStatus.generationError': error.message
          });
        } catch (updateError) {
          console.error('Error updating PDF status:', updateError);
        }
      }
      
      throw error;
    }
  }

  /**
   * Generate HTML from Handlebars template
   */
  async generateHTML(templatePath, data) {
    try {
      // Check if template exists, if not use default template
      let template;
      try {
        const templateContent = await fs.readFile(templatePath, 'utf8');
        template = handlebars.compile(templateContent);
      } catch (error) {
        console.warn(`Template not found at ${templatePath}, using default template`);
        template = this.getDefaultTemplate(data.loanType);
      }

      return template(data);
    } catch (error) {
      console.error('Error generating HTML:', error);
      throw error;
    }
  }

  /**
   * Generate PDF from HTML using Puppeteer
   */
  async generatePDFFromHTML(html, options = {}) {
    const browser = await this.initBrowser();
    const page = await browser.newPage();

    try {
      await page.setContent(html, { waitUntil: 'networkidle0' });

      const pdfOptions = {
        format: options.format || 'A4',
        landscape: options.orientation === 'landscape',
        margin: options.margins || {
          top: '20mm',
          right: '20mm',
          bottom: '20mm',
          left: '20mm'
        },
        printBackground: true,
        preferCSSPageSize: true
      };

      const pdfBuffer = await page.pdf(pdfOptions);
      return pdfBuffer;

    } finally {
      await page.close();
    }
  }

  /**
   * Get default template for loan type
   */
  getDefaultTemplate(loanType) {
    const defaultTemplates = {
      vehicle_loan: this.getVehicleLoanTemplate(),
      personal_loan: this.getPersonalLoanTemplate(),
      housing_loan: this.getHousingLoanTemplate(),
      business_loan: this.getBusinessLoanTemplate(),
      education_loan: this.getEducationLoanTemplate(),
      gold_loan: this.getGoldLoanTemplate()
    };

    const templateContent = defaultTemplates[loanType] || this.getGenericTemplate();
    return handlebars.compile(templateContent);
  }

  /**
   * Vehicle Loan Template
   */
  getVehicleLoanTemplate() {
    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Vehicle Loan Application</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
        .header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 20px; margin-bottom: 30px; }
        .section { margin-bottom: 25px; }
        .section-title { background-color: #f0f0f0; padding: 10px; font-weight: bold; margin-bottom: 15px; }
        .field-row { display: flex; margin-bottom: 10px; }
        .field-label { width: 200px; font-weight: bold; }
        .field-value { flex: 1; }
        .signature-section { margin-top: 50px; display: flex; justify-content: space-between; }
        .signature-box { width: 200px; text-align: center; border-top: 1px solid #333; padding-top: 10px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>VEHICLE LOAN APPLICATION</h1>
        <p>Application ID: {{applicationId}}</p>
        <p>Date: {{formatDate submissionDate}}</p>
    </div>

    <div class="section">
        <div class="section-title">PERSONAL INFORMATION</div>
        <div class="field-row">
            <div class="field-label">Full Name:</div>
            <div class="field-value">{{applicant_first_name}} {{applicant_middle_name}} {{applicant_last_name}}</div>
        </div>
        <div class="field-row">
            <div class="field-label">Email:</div>
            <div class="field-value">{{applicant_email}}</div>
        </div>
        <div class="field-row">
            <div class="field-label">Phone:</div>
            <div class="field-value">{{formatPhone applicant_phone}}</div>
        </div>
        <div class="field-row">
            <div class="field-label">Date of Birth:</div>
            <div class="field-value">{{formatDate applicant_dob}}</div>
        </div>
        <div class="field-row">
            <div class="field-label">Gender:</div>
            <div class="field-value">{{capitalize applicant_gender}}</div>
        </div>
        <div class="field-row">
            <div class="field-label">Marital Status:</div>
            <div class="field-value">{{capitalize applicant_marital_status}}</div>
        </div>
    </div>

    <div class="section">
        <div class="section-title">ADDRESS INFORMATION</div>
        <div class="field-row">
            <div class="field-label">Address:</div>
            <div class="field-value">{{home_address_line1}}, {{home_city}} - {{home_pincode}}</div>
        </div>
    </div>

    <div class="section">
        <div class="section-title">VEHICLE DETAILS</div>
        <div class="field-row">
            <div class="field-label">Vehicle Type:</div>
            <div class="field-value">{{capitalize vehicle_type}}</div>
        </div>
        <div class="field-row">
            <div class="field-label">Make & Model:</div>
            <div class="field-value">{{vehicle_make}} {{vehicle_model}}</div>
        </div>
        <div class="field-row">
            <div class="field-label">Vehicle Price:</div>
            <div class="field-value">{{formatCurrency vehicle_price}}</div>
        </div>
        <div class="field-row">
            <div class="field-label">Down Payment:</div>
            <div class="field-value">{{formatCurrency down_payment}}</div>
        </div>
    </div>

    <div class="section">
        <div class="section-title">FINANCIAL INFORMATION</div>
        <div class="field-row">
            <div class="field-label">Monthly Income:</div>
            <div class="field-value">{{formatCurrency monthly_income}}</div>
        </div>
        <div class="field-row">
            <div class="field-label">Company Name:</div>
            <div class="field-value">{{company_name}}</div>
        </div>
        <div class="field-row">
            <div class="field-label">Work Experience:</div>
            <div class="field-value">{{work_experience}} years</div>
        </div>
    </div>

    <div class="section">
        <div class="section-title">LOAN DETAILS</div>
        <div class="field-row">
            <div class="field-label">Loan Amount:</div>
            <div class="field-value">{{formatCurrency loan_amount}}</div>
        </div>
        <div class="field-row">
            <div class="field-label">Loan Tenure:</div>
            <div class="field-value">{{loan_tenure}} months</div>
        </div>
    </div>

    <div class="signature-section">
        <div class="signature-box">
            <div>Applicant Signature</div>
        </div>
        <div class="signature-box">
            <div>Branch Manager</div>
        </div>
        <div class="signature-box">
            <div>Date</div>
        </div>
    </div>
</body>
</html>`;
  }

  /**
   * Personal Loan Template
   */
  getPersonalLoanTemplate() {
    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Personal Loan Application</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
        .header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 20px; margin-bottom: 30px; }
        .section { margin-bottom: 25px; }
        .section-title { background-color: #f0f0f0; padding: 10px; font-weight: bold; margin-bottom: 15px; }
        .field-row { display: flex; margin-bottom: 10px; }
        .field-label { width: 200px; font-weight: bold; }
        .field-value { flex: 1; }
        .signature-section { margin-top: 50px; display: flex; justify-content: space-between; }
        .signature-box { width: 200px; text-align: center; border-top: 1px solid #333; padding-top: 10px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>PERSONAL LOAN APPLICATION</h1>
        <p>Application ID: {{applicationId}}</p>
        <p>Date: {{formatDate submissionDate}}</p>
    </div>

    <div class="section">
        <div class="section-title">APPLICANT INFORMATION</div>
        <div class="field-row">
            <div class="field-label">Full Name:</div>
            <div class="field-value">{{applicant_first_name}} {{applicant_last_name}}</div>
        </div>
        <div class="field-row">
            <div class="field-label">Email:</div>
            <div class="field-value">{{applicant_email}}</div>
        </div>
        <div class="field-row">
            <div class="field-label">Phone:</div>
            <div class="field-value">{{formatPhone applicant_phone}}</div>
        </div>
    </div>

    <div class="section">
        <div class="section-title">EMPLOYMENT DETAILS</div>
        <div class="field-row">
            <div class="field-label">Company Name:</div>
            <div class="field-value">{{company_name}}</div>
        </div>
        <div class="field-row">
            <div class="field-label">Monthly Income:</div>
            <div class="field-value">{{formatCurrency monthly_income}}</div>
        </div>
    </div>

    <div class="section">
        <div class="section-title">LOAN REQUIREMENTS</div>
        <div class="field-row">
            <div class="field-label">Loan Amount:</div>
            <div class="field-value">{{formatCurrency loan_amount}}</div>
        </div>
        <div class="field-row">
            <div class="field-label">Purpose:</div>
            <div class="field-value">{{capitalize loan_purpose}}</div>
        </div>
    </div>

    <div class="signature-section">
        <div class="signature-box">
            <div>Applicant Signature</div>
        </div>
        <div class="signature-box">
            <div>Branch Manager</div>
        </div>
        <div class="signature-box">
            <div>Date</div>
        </div>
    </div>
</body>
</html>`;
  }

  /**
   * Generic template for other loan types
   */
  getGenericTemplate() {
    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Loan Application</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
        .header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 20px; margin-bottom: 30px; }
        .content { margin-bottom: 50px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>LOAN APPLICATION</h1>
        <p>Application ID: {{applicationId}}</p>
        <p>Date: {{formatDate submissionDate}}</p>
    </div>
    <div class="content">
        <p>This is a generic loan application template. Please create a specific template for {{loanType}}.</p>
    </div>
</body>
</html>`;
  }

  // Placeholder methods for other loan types
  getHousingLoanTemplate() { return this.getGenericTemplate(); }
  getBusinessLoanTemplate() { return this.getGenericTemplate(); }
  getEducationLoanTemplate() { return this.getGenericTemplate(); }
  getGoldLoanTemplate() { return this.getGenericTemplate(); }
}

module.exports = new PDFGeneratorService();
