const mongoose = require('mongoose');

/**
 * Formats Mongoose validation errors into a more readable format
 * @param {Error} error - Mongoose validation error
 * @returns {Object} Formatted error object
 */
const mongooseValidateError = (error) => {
  if (!(error instanceof mongoose.Error.ValidationError)) {
    return error;
  }

  const formattedErrors = {};
  for (const field in error.errors) {
    if (error.errors.hasOwnProperty(field)) {
      formattedErrors[field] = error.errors[field].message;
    }
  }

  return {
    name: 'ValidationError',
    message: 'Validation failed',
    errors: formattedErrors,
    statusCode: 400
  };
};

/**
 * Paginates a Mongoose model query
 * @param {Model} Model - Mongoose model
 * @param {Object} query - Query object
 * @param {Object} options - Pagination options { page, limit, sort, select }
 * @returns {Promise<Object>} Paginated results
 */
const paginateModel = async (Model, query = {}, options = {}) => {
  const page = parseInt(options.page, 10) || 1;
  const limit = parseInt(options.limit, 10) || 10;
  const skip = (page - 1) * limit;
  const sort = options.sort || { createdAt: -1 };
  const select = options.select || '';

  const [docs, total] = await Promise.all([
    Model.find(query)
      .select(select)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .lean(),
    Model.countDocuments(query)
  ]);

  const totalPages = Math.ceil(total / limit);
  const hasNextPage = page < totalPages;
  const hasPreviousPage = page > 1;

  return {
    docs,
    total,
    limit,
    page,
    totalPages,
    hasNextPage,
    hasPreviousPage,
    nextPage: hasNextPage ? page + 1 : null,
    prevPage: hasPreviousPage ? page - 1 : null
  };
};

/**
 * Soft deletes a document by ID
 * @param {Model} Model - Mongoose model
 * @param {string} id - Document ID
 * @param {string} userId - User ID performing the deletion
 * @returns {Promise<Object>} Updated document
 */
const softDeleteById = async (Model, id, userId) => {
  return Model.findByIdAndUpdate(
    id,
    { 
      isDeleted: true,
      deletedAt: new Date(),
      deletedBy: userId 
    },
    { new: true }
  );
};

module.exports = {
  mongooseValidateError,
  paginateModel,
  softDeleteById
};
