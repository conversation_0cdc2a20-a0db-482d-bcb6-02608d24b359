const jwt = require("jsonwebtoken");

const auth = (roles) => {
  return (req, res, next) => {
    try {
      const token = req.headers.authorization?.split(" ")[1];

      if (!token) {
        return res.status(401).json({ error: "Authentication required" });
      }

      const decoded = jwt.verify(token, process.env.JWT_SECRET);

      if (!roles.includes(decoded.role)) {
        return res
          .status(403)
          .json({ error: "Unauthorized: Insufficient permissions" });
      }

      // Ensure consistent user object structure
      req.user = {
        userId: decoded.userId || decoded.id,
        email: decoded.email,
        role: decoded.role,
        permissions: decoded.permissions || [],
        ...decoded // Include any other properties from the token
      };
      next();
    } catch (error) {
      res.status(401).json({ error: "Invalid token" });
    }
  };
};

module.exports = {
  auth,
  isSuperAdmin: auth(["super_admin"]),
  isDeputyManager: auth(["deputy_manager"]),
  isSuperAdminOrDeputyManager: auth(["super_admin", "deputy_manager"]),
};
