/**
 * @swagger
 * tags:
 *   name: User Management
 *   description: User management and authentication endpoints
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     User:
 *       type: object
 *       properties:
 *         _id:
 *           type: string
 *           example: 5f8d0d55b54764421b7156c3
 *           description: The auto-generated ID of the user
 *         email:
 *           type: string
 *           format: email
 *           example: <EMAIL>
 *           description: User's email address (must be unique)
 *         role:
 *           type: string
 *           enum: [super_admin, general_manager, branch_manager, deputy_manager]
 *           example: branch_manager
 *           description: |
 *             User's role in the system. Possible values:
 *             - super_admin: Full system access
 *             - general_manager: Full system access with approval rights
 *             - branch_manager: Manages a specific branch
 *             - deputy_manager: Can create and submit applications
 *         firstName:
 *           type: string
 *           example: John
 *           minLength: 2
 *           maxLength: 50
 *           description: User's first name
 *         lastName:
 *           type: string
 *           example: Doe
 *           minLength: 2
 *           maxLength: 50
 *           description: User's last name
 *         isActive:
 *           type: boolean
 *           example: true
 *           description: Whether the user account is active
 *         branch:
 *           type: string
 *           example: 5f8d0d55b54764421b7156c4
 *           description: ID of the branch the user is assigned to (required for branch_manager and deputy_manager roles)
 *         phone:
 *           type: string
 *           example: "+**********"
 *           pattern: '^\+?[1-9]\d{1,14}$'
 *           description: User's phone number in E.164 format
 *         lastLogin:
 *           type: string
 *           format: date-time
 *           example: "2023-08-28T10:30:00Z"
 *           description: Timestamp of the user's last login
 *         createdAt:
 *           type: string
 *           format: date-time
 *           example: "2023-08-20T14:30:00Z"
 *           readOnly: true
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           example: "2023-08-25T09:15:00Z"
 *           readOnly: true
 * 
 *     LoginRequest:
 *       type: object
 *       required:
 *         - email
 *         - password
 *       properties:
 *         email:
 *           type: string
 *           format: email
 *           example: <EMAIL>
 *           description: User's registered email address
 *         password:
 *           type: string
 *           format: password
 *           minLength: 8
 *           example: "Str0ngP@ssw0rd"
 *           description: User's password (minimum 8 characters, must contain uppercase, lowercase, number, and special character)
 * 
 *     LoginResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *           example: true
 *         token:
 *           type: string
 *           example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI1ZjhkMGQ1NWI1NDc2NDQyMWI3MTU2YzMiLCJyb2xlIjoiYnJhbmNoX21hbmFnZXIiLCJpYXQiOjE2MDAwMDAwMDAsImV4cCI6MTYwMDAwMzYwMH0.abc123def456"
 *           description: JWT token for authentication. Include this in the Authorization header for protected routes.
 *         user:
 *           $ref: '#/components/schemas/User'
 * 
 *     RegisterRequest:
 *       type: object
 *       required:
 *         - email
 *         - password
 *         - role
 *         - firstName
 *         - lastName
 *       properties:
 *         email:
 *           type: string
 *           format: email
 *           example: <EMAIL>
 *         password:
 *           type: string
 *           format: password
 *           minLength: 8
 *           example: "P@ssw0rd123"
 *         role:
 *           type: string
 *           enum: [super_admin, general_manager, branch_manager, deputy_manager]
 *           example: deputy_manager
 *         firstName:
 *           type: string
 *           minLength: 2
 *           maxLength: 50
 *           example: "Jane"
 *         lastName:
 *           type: string
 *           minLength: 2
 *           maxLength: 50
 *           example: "Smith"
 *         branch:
 *           type: string
 *           example: "5f8d0d55b54764421b7156c4"
 *           description: Required for branch_manager and deputy_manager roles
 * 
 *     ErrorResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *           example: false
 *         message:
 *           type: string
 *           example: "Validation failed"
 *         errors:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               field:
 *                 type: string
 *                 example: "email"
 *               message:
 *                 type: string
 *                 example: "Invalid email format"
 */

/**
 * @swagger
 * /api/auth/register:
 *   post:
 *     summary: Register a new user (Super Admin and General Manager only)
 *     description: |
 *       Creates a new user account with the specified role and details.
 *       
 *       ### Role Permissions:
 *       - **Super Admin**: Can create any role
 *       - **General Manager**: Can create branch_manager and deputy_manager roles
 *       - Others: Not authorized to create users
 *       
 *       ### Password Requirements:
 *       - Minimum 8 characters
 *       - At least one uppercase letter
 *       - At least one lowercase letter
 *       - At least one number
 *       - At least one special character
 *     tags: [User Management]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/RegisterRequest'
 *           example:
 *             email: <EMAIL>
 *             password: P@ssw0rd123
 *             role: deputy_manager
 *             firstName: Jane
 *             lastName: Smith
 *             branch: 5f8d0d55b54764421b7156c4
 *     responses:
 *       201:
 *         description: User created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: User created successfully
 *                 data:
 *                   $ref: '#/components/schemas/User'
 *       400:
 *         description: Validation error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "Validation failed"
 *               errors:
 *                 - field: "email"
 *                   message: "Invalid email format"
 *                 - field: "password"
 *                   message: "Password must be at least 8 characters long"
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         description: Forbidden - Insufficient permissions
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "You don't have permission to create users with role: super_admin"
 *       409:
 *         description: Conflict - Email already exists
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "Email already registered"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "Internal server error"
 */

/**
 * @swagger
 * /api/auth/login:
 *   post:
 *     summary: Authenticate user and get JWT token
 *     description: |
 *       Authenticates a user and returns a JWT token for accessing protected routes.
 *       
 *       The token should be included in the `Authorization` header for subsequent requests:
 *       ```
 *       Authorization: Bearer <token>
 *       ```
 *     tags: [User Management]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/LoginRequest'
 *           example:
 *             email: <EMAIL>
 *             password: Str0ngP@ssw0rd
 *     responses:
 *       200:
 *         description: Login successful
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/LoginResponse'
 *             example:
 *               success: true
 *               token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
 *               user:
 *                 _id: "5f8d0d55b54764421b7156c3"
 *                 email: "<EMAIL>"
 *                 role: "branch_manager"
 *                 firstName: "John"
 *                 lastName: "Doe"
 *                 isActive: true
 *                 branch: "5f8d0d55b54764421b7156c4"
 *                 lastLogin: "2023-08-28T10:30:00Z"
 *                 createdAt: "2023-08-20T14:30:00Z"
 *                 updatedAt: "2023-08-25T09:15:00Z"
 *       400:
 *         description: Invalid request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             examples:
 *               invalidEmail:
 *                 value:
 *                   success: false
 *                   message: "Validation failed"
 *                   errors:
 *                     - field: "email"
 *                       message: "Invalid email format"
 *               missingFields:
 *                 value:
 *                   success: false
 *                   message: "Validation failed"
 *                   errors:
 *                     - field: "email"
 *                       message: "Email is required"
 *                     - field: "password"
 *                       message: "Password is required"
 *       401:
 *         description: Authentication failed
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             examples:
 *               invalidCredentials:
 *                 value:
 *                   success: false
 *                   message: "Invalid email or password"
 *               inactiveAccount:
 *                 value:
 *                   success: false
 *                   message: "Account is inactive. Please contact administrator."
 *       429:
 *         description: Too many login attempts
 *         content:
 *           application/json:
 *             example:
 *               success: false
 *               message: "Too many login attempts. Please try again in 15 minutes."
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */

/**
 * @swagger
 * /api/users:
 *   get:
 *     summary: Get all users (Super Admin and General Manager only)
 *     description: |
 *       Retrieves a paginated list of users with filtering, sorting, and search capabilities.
 *       
 *       ### Access Control:
 *       - **Super Admin**: Can view all users across all branches
 *       - **General Manager**: Can view all users in their assigned branch
 *       - **Others**: Not authorized
 *       
 *       ### Response Metadata:
 *       The response includes pagination metadata for easier client-side pagination.
 *     tags: [User Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 10
 *         description: Number of items per page
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           enum: [createdAt, updatedAt, firstName, lastName, email, role]
 *           default: createdAt
 *         description: Field to sort by
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *           default: desc
 *         description: Sort order (asc/desc)
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term to filter users by name or email
 *       - in: query
 *         name: role
 *         schema:
 *           type: string
 *           enum: [super_admin, general_manager, branch_manager, deputy_manager]
 *         description: Filter users by role
 *       - in: query
 *         name: branch
 *         schema:
 *           type: string
 *         description: Filter users by branch ID
 *       - in: query
 *         name: isActive
 *         schema:
 *           type: boolean
 *         description: Filter by account active status
 *       - in: query
 *         name: createdAfter
 *         schema:
 *           type: string
 *           format: date-time
 *         description: Filter users created after this date (ISO 8601 format)
 *       - in: query
 *         name: createdBefore
 *         schema:
 *           type: string
 *           format: date-time
 *         description: Filter users created before this date (ISO 8601 format)
 *     responses:
 *       200:
 *         description: Paginated list of users
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/User'
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     total:
 *                       type: integer
 *                       example: 42
 *                       description: Total number of users matching the query
 *                     page:
 *                       type: integer
 *                       example: 1
 *                       description: Current page number
 *                     limit:
 *                       type: integer
 *                       example: 10
 *                       description: Number of items per page
 *                     totalPages:
 *                       type: integer
 *                       example: 5
 *                       description: Total number of pages
 *       400:
 *         $ref: '#/components/responses/ValidationError'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 * 
 *   post:
 *     summary: Create a new user (Super Admin and General Manager only)
 *     tags: [User Management]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/User'
 *     responses:
 *       201:
 *         description: User created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/User'
 *       400:
 *         $ref: '#/components/responses/ValidationError'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 */

/**
 * @swagger
 * /api/users/{id}:
 *   get:
 *     summary: Get user by ID
 *     tags: [User Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: User ID
 *     responses:
 *       200:
 *         description: User details
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/User'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       404:
 *         description: User not found
 * 
 *   put:
 *     summary: Update user details
 *     tags: [User Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: User ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               firstName:
 *                 type: string
 *               lastName:
 *                 type: string
 *               email:
 *                 type: string
 *                 format: email
 *               role:
 *                 type: string
 *                 enum: [super_admin, general_manager, branch_manager, deputy_manager]
 *               branch:
 *                 type: string
 *               isActive:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: User updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/User'
 *       400:
 *         $ref: '#/components/responses/ValidationError'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 * 
 *   delete:
 *     summary: Delete a user (Super Admin only)
 *     tags: [User Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: User ID
 *     responses:
 *       200:
 *         description: User deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       404:
 *         description: User not found
 */
