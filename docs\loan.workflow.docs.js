/**
 * @swagger
 * tags:
 *   name: Loan Workflow
 *   description: Loan application workflow management
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     WorkflowAction:
 *       type: object
 *       required:
 *         - action
 *       properties:
 *         action:
 *           type: string
 *           enum: [submit, approve, reject, return, disburse]
 *           description: The action to perform on the loan application
 *         comments:
 *           type: string
 *           description: Optional comments for the action
 * 
 *     WorkflowHistory:
 *       type: object
 *       properties:
 *         fromStatus:
 *           type: string
 *           description: Previous status of the application
 *         toStatus:
 *           type: string
 *           description: New status after the action
 *         action:
 *           type: string
 *           description: Action performed
 *         performedBy:
 *           type: string
 *           description: ID of the user who performed the action
 *         comments:
 *           type: string
 *           description: Comments provided with the action
 *         timestamp:
 *           type: string
 *           format: date-time
 *           description: When the action was performed
 * 
 *     LoanApplication:
 *       type: object
 *       properties:
 *         _id:
 *           type: string
 *           description: The auto-generated ID of the loan application
 *         status:
 *           type: string
 *           enum: [draft, pending_approval, approved, rejected, disbursed]
 *           description: Current status of the application
 *         workflow:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/WorkflowHistory'
 *         createdBy:
 *           type: string
 *           description: ID of the user who created the application
 *         branch:
 *           type: string
 *           description: ID of the branch where the application was created
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 */

/**
 * @swagger
 * /api/loans/{id}/status:
 *   post:
 *     summary: Update loan application status
 *     tags: [Loan Workflow]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Loan application ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/WorkflowAction'
 *     responses:
 *       200:
 *         description: Status updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Application approved successfully"
 *                 data:
 *                   $ref: '#/components/schemas/LoanApplication'
 *       400:
 *         $ref: '#/components/responses/ValidationError'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       404:
 *         description: Loan application not found
 *       500:
 *         description: Internal server error
 */

/**
 * @swagger
 * /api/loans/{id}:
 *   get:
 *     summary: Get loan application by ID
 *     tags: [Loan Workflow]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Loan application ID
 *     responses:
 *       200:
 *         description: Loan application details
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/LoanApplication'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       404:
 *         description: Loan application not found
 */

/**
 * @swagger
 * /api/loans:
 *   get:
 *     summary: List loan applications with filtering and pagination
 *     tags: [Loan Workflow]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [draft, pending_approval, approved, rejected, disbursed]
 *         description: Filter by status
 *       - in: query
 *         name: branch
 *         schema:
 *           type: string
 *         description: Filter by branch ID
 *       - in: query
 *         name: createdBy
 *         schema:
 *           type: string
 *         description: Filter by creator ID
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Items per page
 *     responses:
 *       200:
 *         description: List of loan applications
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/LoanApplication'
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     total:
 *                       type: integer
 *                     page:
 *                       type: integer
 *                     pages:
 *                       type: integer
 *                     limit:
 *                       type: integer
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 */
