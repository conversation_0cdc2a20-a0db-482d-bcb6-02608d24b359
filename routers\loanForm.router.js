const express = require('express');
const router = express.Router();
const { authMiddleware, roleMiddleware } = require('../middleware/auth.middleware');
const {
  getFormSchema,
  getAllFormSchemas,
  saveFormData,
  getFormData,
  validateFormData,
  getUserFormSubmissions
} = require('../controllers/loanForm.controller');
const pdfGeneratorService = require('../services/pdfGenerator.service');

/**
 * @swagger
 * components:
 *   schemas:
 *     LoanFormSchema:
 *       type: object
 *       properties:
 *         loanType:
 *           type: string
 *           enum: [vehicle_loan, housing_loan, personal_loan, business_loan, education_loan, gold_loan]
 *         loanTypeName:
 *           type: string
 *         version:
 *           type: string
 *         sections:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               sectionName:
 *                 type: string
 *               title:
 *                 type: string
 *               order:
 *                 type: number
 *               fields:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     fieldName:
 *                       type: string
 *                     fieldType:
 *                       type: string
 *                     label:
 *                       type: string
 *                     isRequired:
 *                       type: boolean
 *                     validation:
 *                       type: object
 *                     options:
 *                       type: array
 */

/**
 * @swagger
 * /loan-forms/schemas:
 *   get:
 *     summary: Get all available loan form schemas
 *     tags: [Loan Forms]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Form schemas retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     schemas:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/LoanFormSchema'
 */
router.get('/schemas', 
  authMiddleware, 
  roleMiddleware(['deputy_manager', 'branch_manager', 'general_manager', 'super_admin']), 
  getAllFormSchemas
);

/**
 * @swagger
 * /loan-forms/schema/{loanType}:
 *   get:
 *     summary: Get form schema for specific loan type
 *     tags: [Loan Forms]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: loanType
 *         required: true
 *         schema:
 *           type: string
 *           enum: [vehicle_loan, housing_loan, personal_loan, business_loan, education_loan, gold_loan]
 *     responses:
 *       200:
 *         description: Form schema retrieved successfully
 *       404:
 *         description: Schema not found for loan type
 */
router.get('/schema/:loanType', 
  authMiddleware, 
  roleMiddleware(['deputy_manager', 'branch_manager', 'general_manager', 'super_admin']), 
  getFormSchema
);

/**
 * @swagger
 * /loan-forms/data:
 *   post:
 *     summary: Save or update form data
 *     tags: [Loan Forms]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - loanType
 *               - formData
 *             properties:
 *               loanType:
 *                 type: string
 *                 enum: [vehicle_loan, housing_loan, personal_loan, business_loan, education_loan, gold_loan]
 *               formData:
 *                 type: object
 *                 description: Form field values as key-value pairs
 *               workflowId:
 *                 type: string
 *                 description: Workflow ID if updating existing application
 *               isDraft:
 *                 type: boolean
 *                 default: true
 *                 description: Whether to save as draft or submit
 *     responses:
 *       200:
 *         description: Form data saved successfully
 *       400:
 *         description: Validation errors
 *       404:
 *         description: Form schema not found
 */
router.post('/data', 
  authMiddleware, 
  roleMiddleware(['deputy_manager']), 
  saveFormData
);

/**
 * @swagger
 * /loan-forms/data/{workflowId}:
 *   get:
 *     summary: Get form data by workflow ID
 *     tags: [Loan Forms]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: workflowId
 *         required: true
 *         schema:
 *           type: string
 *         description: Workflow ID
 *     responses:
 *       200:
 *         description: Form data retrieved successfully
 *       403:
 *         description: Access denied
 *       404:
 *         description: Form data not found
 */
router.get('/data/:workflowId', 
  authMiddleware, 
  roleMiddleware(['deputy_manager', 'branch_manager', 'general_manager', 'super_admin']), 
  getFormData
);

/**
 * @swagger
 * /loan-forms/validate/{workflowId}:
 *   post:
 *     summary: Validate form data
 *     tags: [Loan Forms]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: workflowId
 *         required: true
 *         schema:
 *           type: string
 *         description: Workflow ID
 *     responses:
 *       200:
 *         description: Validation completed
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     isValid:
 *                       type: boolean
 *                     errors:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           field:
 *                             type: string
 *                           message:
 *                             type: string
 *       404:
 *         description: Form data not found
 */
router.post('/validate/:workflowId', 
  authMiddleware, 
  roleMiddleware(['deputy_manager', 'branch_manager', 'general_manager', 'super_admin']), 
  validateFormData
);

/**
 * @swagger
 * /loan-forms/my-submissions:
 *   get:
 *     summary: Get current user's form submissions
 *     tags: [Loan Forms]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [draft, submitted, validated, pdf_generated, completed, error]
 *         description: Filter by status
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Items per page
 *     responses:
 *       200:
 *         description: Form submissions retrieved successfully
 */
router.get('/my-submissions', 
  authMiddleware, 
  roleMiddleware(['deputy_manager', 'branch_manager', 'general_manager', 'super_admin']), 
  getUserFormSubmissions
);

/**
 * @swagger
 * /loan-forms/generate-pdf/{formDataId}:
 *   post:
 *     summary: Generate PDF for form data
 *     tags: [Loan Forms]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: formDataId
 *         required: true
 *         schema:
 *           type: string
 *         description: Form data ID
 *     responses:
 *       200:
 *         description: PDF generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     pdfUrl:
 *                       type: string
 *                     fileName:
 *                       type: string
 *       404:
 *         description: Form data not found
 *       500:
 *         description: PDF generation failed
 */
router.post('/generate-pdf/:formDataId', 
  authMiddleware, 
  roleMiddleware(['deputy_manager', 'branch_manager', 'general_manager', 'super_admin']), 
  async (req, res) => {
    try {
      const { formDataId } = req.params;
      const { options = {} } = req.body;

      const result = await pdfGeneratorService.generatePDF(formDataId, options);

      res.json({
        success: true,
        message: 'PDF generated successfully',
        data: {
          pdfUrl: result.pdfUrl,
          fileName: result.fileName,
          pdfPath: result.pdfPath
        }
      });

    } catch (error) {
      console.error('Error generating PDF:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to generate PDF',
        error: error.message
      });
    }
  }
);

/**
 * @swagger
 * /loan-forms/download-pdf/{formDataId}:
 *   get:
 *     summary: Download PDF for form data
 *     tags: [Loan Forms]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: formDataId
 *         required: true
 *         schema:
 *           type: string
 *         description: Form data ID
 *     responses:
 *       200:
 *         description: PDF file
 *         content:
 *           application/pdf:
 *             schema:
 *               type: string
 *               format: binary
 *       404:
 *         description: PDF not found
 */
router.get('/download-pdf/:formDataId', 
  authMiddleware, 
  roleMiddleware(['deputy_manager', 'branch_manager', 'general_manager', 'super_admin']), 
  async (req, res) => {
    try {
      const { formDataId } = req.params;
      const LoanFormData = require('../models/loanFormData.model');
      
      const formData = await LoanFormData.findById(formDataId);
      
      if (!formData) {
        return res.status(404).json({
          success: false,
          message: 'Form data not found'
        });
      }

      if (!formData.pdfStatus.isGenerated || !formData.pdfStatus.pdfPath) {
        return res.status(404).json({
          success: false,
          message: 'PDF not generated yet'
        });
      }

      const fs = require('fs');
      const path = require('path');
      
      if (!fs.existsSync(formData.pdfStatus.pdfPath)) {
        return res.status(404).json({
          success: false,
          message: 'PDF file not found'
        });
      }

      const fileName = path.basename(formData.pdfStatus.pdfPath);
      
      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);
      
      const fileStream = fs.createReadStream(formData.pdfStatus.pdfPath);
      fileStream.pipe(res);

    } catch (error) {
      console.error('Error downloading PDF:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to download PDF',
        error: error.message
      });
    }
  }
);

module.exports = router;
