const express = require("express");
const router = express.Router();
const {
  isSuper<PERSON><PERSON><PERSON>,
  isDeputy<PERSON>anager,
  isSuperAdminOrDeputyManager,
} = require("./middleware/role.middleware");
const { authMiddleware } = require("./middleware/auth.middleware");
const pApplication = require("./controllers/PrimaryApplication.controller");
const location = require("./controllers/location.controller");
const loginHandler = require("./controllers/login.controller");
const productHandler = require("./controllers/product.controller");

// Import module routers
const authRouter = require("./routers/auth.router");
const branchRouter = require("./routers/branch.router");
const applicationRouter = require("./routers/application.router");
const loanWorkflowRouter = require("./routers/loanWorkflow.router");
const loanFormRouter = require("./routers/loanForm.router");
const integratedLoanApplicationRouter = require("./routers/integratedLoanApplication.router");
const frontendIntegratedApplicationRouter = require("./routers/frontendIntegratedApplication.router");

// Mount routers
router.use("/auth", authRouter);
router.use("/branch", branchRouter);
router.use("/primary-application", applicationRouter);
router.use("/loan-workflow", loanWorkflowRouter);
router.use("/loan-forms", loanFormRouter);
router.use("/applications", integratedLoanApplicationRouter);
router.use("/frontend-applications", frontendIntegratedApplicationRouter);

// Base router that mounts all other routers
module.exports = router;
/**
 * @swagger
 * /primary-application/collateral-details:
 *   post:
 *     summary: Save collateral details for an application
 *     tags: [Applications]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CollateralDetails'
 *     responses:
 *       200:
 *         description: Collateral details saved successfully
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Unauthorized
 */
router.post(
  "/primary-application/collateral-details",
  authMiddleware,
  pApplication.saveCollateralDetails
);



/**
 * @swagger
 * /primary-application/document-details:
 *   post:
 *     summary: Save document details for an application
 *     tags: [Applications]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/DocumentDetails'
 *     responses:
 *       200:
 *         description: Document details saved successfully
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Unauthorized
 */
router.post(
  "/primary-application/document-details",
  authMiddleware,
  pApplication.saveDocumentDetails
);

/**
 * @swagger
 * /primary-application/loan-type:
 *   get:
 *     summary: Get Loan Type
 *     tags: [Applications]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of Loan Type
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: string
 *                   groupName:
 *                     type: string
 *                   name:
 *                     type: string
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: State not found
 */
router.get(
  "/primary-application/loan-type",
  authMiddleware,
  pApplication.getLoanType
);

/**
 * @swagger
 * /primary-application:
 *   get:
 *     summary: get primary applications
 *     tags: [Applications]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/PrimaryApplications'
 *     responses:
 *       200:
 *         description: primary application data
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Unauthorized
 */
router.get(
  "/primary-application",
  authMiddleware,
  pApplication.getPrimaryApplicationList
);

/**
 * @swagger
 * /primary-application/:primaryApplicationId:
 *   get:
 *     summary: get primary application
 *     tags: [Applications]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/PrimaryApplications'
 *     responses:
 *       200:
 *         description: primary application data
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Unauthorized
 */
router.get(
  "/primary-application/:primaryApplicationId",
  pApplication.getPrimaryApplication
);

/**
 * @swagger
 * /primary-application/document-details:
 *   post:
 *     summary: Save document details for an application
 *     tags: [Applications]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/DocumentDetails'
 *     responses:
 *       200:
 *         description: Document details saved successfully
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Unauthorized
 */
router.patch(
  "/primary-application/:id/status",
  pApplication.transitionPrimaryApplicationStatus
);

// Location APIs
/**
 * @swagger
 * /countries:
 *   get:
 *     summary: Get list of all countries
 *     tags: [Locations]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of countries
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   code:
 *                     type: string
 *                   name:
 *                     type: string
 *       401:
 *         description: Unauthorized
 */
router.get("/countries", authMiddleware, location.getCountries);

/**
 * @swagger
 * /state/{countryCode}:
 *   get:
 *     summary: Get states for a country
 *     tags: [Locations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: countryCode
 *         schema:
 *           type: string
 *         required: true
 *         description: Country code
 *     responses:
 *       200:
 *         description: Successfully retrieved state data
 *         content:
 *           application/json:
 *             oneOf:
 *               - type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                     name:
 *                       type: string
 *               - type: object
 *                 properties:
 *                   id:
 *                     type: string
 *                   name:
 *                     type: string
 *                   countryCode:
 *                     type: string
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Country not found
 */
router.get("/state/:countryCode", authMiddleware, location.getState);

/**
 * @swagger
 * /city/{stateId}:
 *   get:
 *     summary: Get cities for a state
 *     tags: [Locations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: stateId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the state
 *     responses:
 *       200:
 *         description: Successfully retrieved cities
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: string
 *                   name:
 *                     type: string
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: State not found
 */
router.get(
  "/city/:stateId(\\w+)",
  authMiddleware,
  isSuperAdminOrDeputyManager, // Changed from isSuperAdminOrEmployee
  location.getCity
);

// Admin test endpoint
/**
 * @swagger
 * /create-amdin-test:
 *   get:
 *     summary: '[TEST] Create a super admin (for testing only)'
 *     tags: [Admin]
 *     responses:
 *       200:
 *         description: Admin created
 *       500:
 *         description: Internal server error
 */
router.get("/create-amdin-test", loginHandler.createSuperAdmin);

/**
 * @swagger
 * /test-endpoint:
 *   get:
 *     summary: Test endpoint with authentication
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Successful test
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 msg:
 *                   type: string
 *                   example: success
 *       401:
 *         description: Unauthorized
 */
router.get("/test-endpoint", authMiddleware, async (req, res) => {
  console.log(req.user);
  return res.json({ msg: "success" });
});

// Product APIs
/**
 * @swagger
 * /product/get:
 *   get:
 *     summary: Get list of all products
 *     tags: [Products]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of products
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Products'
 *       401:
 *         description: Unauthorized
 */
router.get("/product/get", authMiddleware, productHandler.getProduct);

/**
 * @swagger
 * /product/get/{id}:
 *   get:
 *     summary: Get a single product by ID
 *     tags: [Products]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The Product ID
 *     responses:
 *       200:
 *         description: Product details
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Product'
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Product not found
 *       500:
 *         description: Internal server error
 */
router.get("/product/get/:id", authMiddleware, productHandler.getProductById);

/**
 * @swagger
 * /product/create:
 *   post:
 *     summary: Create a new product
 *     tags: [Products]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/products'
 *     responses:
 *       201:
 *         description: Product created successfully
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Unauthorized
 */
router.post("/product/create", authMiddleware, productHandler.createProduct);

/**
 * @swagger
 * /product/delete/{id}:
 *   delete:
 *     summary: product a product by ID
 *     tags: [Products]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: string
 *         required: true
 *         description: Products ID
 *     responses:
 *       200:
 *         description: Product deleted successfully
 *       404:
 *         description: Product not found
 *       401:
 *         description: Unauthorized
 */
router.delete(
  "/product/delete/:id",
  authMiddleware,
  productHandler.deleteProduct
);

module.exports = router;
