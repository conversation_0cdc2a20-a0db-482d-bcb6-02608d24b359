require('dotenv').config();
const mongoose = require('mongoose');
const User = require('../models/user.model');
const SuperAdmin = require('../schema/superadmin.model');
const Branch = require('../models/branch.model');
const bcrypt = require('bcryptjs');

// Default values for initial CEO account and branch
const DEFAULTS = {
    // Authentication
    EMAIL: '<EMAIL>',
    PASSWORD: 'Admin@123',
    USERNAME: 'superadmin',

    // Personal Info
    FIRST_NAME: 'Bank',
    LAST_NAME: 'CEO',
    DATE_OF_BIRTH: new Date('1990-01-01'),
    GENDER: 'male',
    PHONE: '**********',

    // Address
    STREET: '123 Main Street',
    CITY: 'Mumbai',
    STATE: 'Maharashtra',
    POSTAL_CODE: '400001',
    COUNTRY: 'India',

    // Employment
    DEPARTMENT: 'Administration',
    DESIGNATION: 'Chief Executive Officer',
    DATE_OF_JOINING: new Date(),

    // Branch
    BRANCH_NAME: 'Head Office',
    BRANCH_CODE: 'HO',
    BRANCH_PHONE: '**********',
    BRANCH_ADDRESS: '123 Main Street, Mumbai, Maharashtra 400001, India'
};

// Function to ensure default branch exists
async function ensureDefaultBranch() {
    const branchCode = process.env.DEFAULT_BRANCH_CODE || DEFAULTS.BRANCH_CODE;

    // Check if branch already exists
    const existingBranch = await Branch.findOne({ code: branchCode });
    if (existingBranch) {
        console.log('Default branch already exists, using existing branch');
        return existingBranch;
    }

    // Create new branch
    const branchData = {
        name: process.env.DEFAULT_BRANCH_NAME || DEFAULTS.BRANCH_NAME,
        code: branchCode,
        email: process.env.DEFAULT_BRANCH_EMAIL || '<EMAIL>',
        phone: process.env.DEFAULT_BRANCH_PHONE || DEFAULTS.BRANCH_PHONE,
        address: {
            street: process.env.DEFAULT_BRANCH_STREET || DEFAULTS.STREET,
            city: process.env.DEFAULT_BRANCH_CITY || DEFAULTS.CITY,
            state: process.env.DEFAULT_BRANCH_STATE || DEFAULTS.STATE,
            postalCode: process.env.DEFAULT_BRANCH_POSTAL_CODE || DEFAULTS.POSTAL_CODE,
            country: process.env.DEFAULT_BRANCH_COUNTRY || DEFAULTS.COUNTRY
        },
        ifscCode: process.env.DEFAULT_BRANCH_IFSC || 'BANK0000001',
        micrCode: process.env.DEFAULT_BRANCH_MICR || '*********',
        isActive: true
    };

    try {
        const branch = new Branch(branchData);
        await branch.save();
        console.log('Default branch created successfully');
        return branch;
    } catch (error) {
        console.error('Error creating default branch:', error);
        throw error;
    }
}

async function createSuperAdmin() {
    // Get values from environment or use defaults
    const superAdminData = {
        // Authentication - only fields that SuperAdmin model supports
        email: process.env.SUPERADMIN_EMAIL || DEFAULTS.EMAIL,
        password: process.env.SUPERADMIN_PASSWORD || DEFAULTS.PASSWORD,
        role: 'super_admin',
        isActive: true,
        lastLogin: new Date()
    };

    // Also create a detailed User record for profile management
    const userProfileData = {
        // Authentication
        email: process.env.SUPERADMIN_EMAIL || DEFAULTS.EMAIL,
        password: process.env.SUPERADMIN_PASSWORD || DEFAULTS.PASSWORD,
        username: process.env.SUPERADMIN_USERNAME || DEFAULTS.USERNAME,

        // Personal Information
        firstName: process.env.SUPERADMIN_FIRST_NAME || DEFAULTS.FIRST_NAME,
        lastName: process.env.SUPERADMIN_LAST_NAME || DEFAULTS.LAST_NAME,
        dateOfBirth: process.env.DATE_OF_BIRTH ? new Date(process.env.DATE_OF_BIRTH) : DEFAULTS.DATE_OF_BIRTH,
        gender: process.env.GENDER || DEFAULTS.GENDER,
        phoneNumber: process.env.PHONE_NUMBER || DEFAULTS.PHONE,

        // Address
        address: {
            street: process.env.ADDRESS_STREET || DEFAULTS.STREET,
            city: process.env.ADDRESS_CITY || DEFAULTS.CITY,
            state: process.env.ADDRESS_STATE || DEFAULTS.STATE,
            postalCode: process.env.ADDRESS_POSTAL_CODE || DEFAULTS.POSTAL_CODE,
            country: process.env.ADDRESS_COUNTRY || DEFAULTS.COUNTRY
        },

        // Employment
        department: process.env.DEPARTMENT || DEFAULTS.DEPARTMENT,
        designation: process.env.DESIGNATION || DEFAULTS.DESIGNATION,
        dateOfJoining: process.env.DATE_OF_JOINING ? new Date(process.env.DATE_OF_JOINING) : DEFAULTS.DATE_OF_JOINING,

        // Role and Access
        role: 'super_admin',
        isActive: true,
        lastLogin: new Date(),

        // System
        createdBy: new mongoose.Types.ObjectId(), // Self-created
        updatedBy: new mongoose.Types.ObjectId(), // Self-updated
        permissions: ['all']
    };

    // Get or create default branch
    const branch = await ensureDefaultBranch();
    userProfileData.branch = branch._id;

    // Validate email format
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(superAdminData.email)) {
        throw new Error('Invalid email format for super admin');
    }

    // Validate username for user profile
    if (!userProfileData.username || userProfileData.username.length < 3) {
        throw new Error('Username must be at least 3 characters long');
    }

    // Check if password meets requirements
    if (superAdminData.password.length < 8 || !/[A-Z]/.test(superAdminData.password) || !/[0-9]/.test(superAdminData.password)) {
        throw new Error('Password must be at least 8 characters long and include at least one uppercase letter and one number');
    }

    // Check if super admin already exists by email
    const existingAdmin = await SuperAdmin.findOne({ email: superAdminData.email });
    if (existingAdmin) {
        console.log('Super admin with this email already exists, skipping creation');
        return existingAdmin;
    }

    // Check if username already exists in User collection
    const existingUserWithUsername = await User.findOne({ username: userProfileData.username });
    if (existingUserWithUsername) {
        console.log(`Username '${userProfileData.username}' is already taken`);
        throw new Error(`Username '${userProfileData.username}' is already taken`);
    }

    // Create super admin in SuperAdmin collection (for authentication)
    const superAdmin = new SuperAdmin(superAdminData);

    try {
        await superAdmin.save();
        console.log('Super admin created successfully in SuperAdmin collection');

        // Also create detailed user profile in User collection
        const userProfile = new User(userProfileData);
        await userProfile.save();
        console.log('User profile created successfully in User collection');

        return superAdmin;
    } catch (error) {
        console.error('Error creating super admin:', error);
        throw error;
    }
}

async function seedDatabase() {
    try {
        // Connect to MongoDB
        await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/los');
        console.log('Connected to MongoDB');

        // Create super admin and ensure default branch exists
        const superAdmin = await createSuperAdmin();

        console.log('\n=== DATABASE SEEDING COMPLETE ===');
        console.log('\nSUPER ADMIN CREDENTIALS:');
        console.log('------------------------------');
        console.log(`Email:    ${superAdmin.email}`);
        console.log(`Password: ${process.env.SUPERADMIN_PASSWORD || DEFAULTS.PASSWORD}`);
        console.log('------------------------------');

        console.log('\nUSER DETAILS:');
        console.log('------------------------------');
        console.log(`Role:     ${superAdmin.role}`);
        console.log(`Branch:   ${process.env.DEFAULT_BRANCH_NAME || DEFAULTS.BRANCH_NAME}`);
        console.log(`Status:   ${superAdmin.isActive ? 'Active' : 'Inactive'}`);
        console.log('------------------------------');

        console.log('\nIMPORTANT SECURITY NOTES:');
        console.log('1. Change the default password immediately after first login');
        console.log('2. Enable two-factor authentication for enhanced security');
        console.log('3. Review and update user permissions as needed\n');

        // Disconnect from MongoDB
        await mongoose.disconnect();
        console.log('Disconnected from MongoDB');
    } catch (error) {
        console.error('\n❌ ERROR SEEDING DATABASE:');
        console.error(error.message);

        if (process.env.NODE_ENV !== 'production') {
            console.error('\nStack trace:', error.stack);

            // Additional debug info for common issues
            if (error.name === 'ValidationError') {
                console.error('\nValidation Errors:');
                for (field in error.errors) {
                    console.error(`- ${field}: ${error.errors[field].message}`);
                }
            }
        }

        process.exit(1);
    }
}

// Run the seed function if this file is executed directly
if (require.main === module) {
    seedDatabase();
} else {
    module.exports = {
        createSuperAdmin,
        ensureDefaultBranch,
        seedDatabase
    };
}
