# 🏦 Loan Approval Workflow System

## 📋 Overview

This document describes the hierarchical loan approval workflow system implemented for the LOS (Loan Origination System) backend. The system enforces a maker-checker pattern with amount-based routing.

## 🔄 Workflow Rules

### **Role Hierarchy:**
1. **Deputy Manager** (Maker) → Creates and submits applications
2. **Branch Manager** (Checker) → Reviews and approves/forwards applications  
3. **General Manager** (Final Approver) → Approves high-value loans

### **Amount-Based Routing:**
- **≤ ₹25 Lakhs**: Branch Manager can approve directly
- **> ₹25 Lakhs**: Must be forwarded to General Manager

## 🎯 Workflow States

| Status | Description | Who Can Act |
|--------|-------------|-------------|
| `draft` | Application being prepared | Deputy Manager |
| `submitted_to_branch_manager` | Pending BM review | Branch Manager |
| `approved_by_branch_manager` | Approved by BM (≤25L only) | - |
| `forwarded_to_general_manager` | Sent to GM for approval | General Manager |
| `approved_by_general_manager` | Final approval by GM | - |
| `rejected` | Application rejected | - |
| `returned_for_changes` | Sent back for modifications | Deputy Manager |

## ⚡ Available Actions

### **Deputy Manager Actions:**
- `save_draft` - Save application as draft
- `submit_to_branch_manager` - Submit for BM review

### **Branch Manager Actions:**
- `approve_by_branch_manager` - Approve (only if ≤25L)
- `forward_to_general_manager` - Forward to GM (required if >25L)
- `reject` - Reject application
- `return_for_changes` - Send back for modifications

### **General Manager Actions:**
- `approve_by_general_manager` - Final approval
- `reject` - Reject application  
- `return_for_changes` - Send back for modifications

## 🚀 API Endpoints

### **Base URL:** `/loan-workflow`

#### **1. Get Workflow Constants**
```http
GET /loan-workflow/constants
Authorization: Bearer {token}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "statuses": { ... },
    "actions": { ... },
    "threshold": 2500000,
    "transitions": { ... }
  }
}
```

#### **2. Create Workflow (Deputy Manager)**
```http
POST /loan-workflow
Authorization: Bearer {token}
Content-Type: application/json

{
  "applicationData": "primaryApplicationId",
  "loanAmount": 1500000,
  "branchId": "branchId" // optional
}
```

#### **3. Get User Workflows**
```http
GET /loan-workflow?status=draft&page=1&limit=10
Authorization: Bearer {token}
```

#### **4. Get Workflow Status**
```http
GET /loan-workflow/{workflowId}/status
Authorization: Bearer {token}
```

**Response includes:**
- Current status and reviewer
- Available actions for current user
- Button states for frontend
- Workflow progress steps
- Notifications and warnings

#### **5. Submit to Branch Manager**
```http
POST /loan-workflow/{workflowId}/submit-to-bm
Authorization: Bearer {token}
Content-Type: application/json

{
  "comments": "Application ready for review"
}
```

#### **6. Branch Manager Actions**
```http
POST /loan-workflow/{workflowId}/branch-manager-action
Authorization: Bearer {token}
Content-Type: application/json

{
  "action": "approve_by_branch_manager", // or forward_to_general_manager, reject, return_for_changes
  "comments": "Approved for processing"
}
```

#### **7. General Manager Actions**
```http
POST /loan-workflow/{workflowId}/general-manager-action
Authorization: Bearer {token}
Content-Type: application/json

{
  "action": "approve_by_general_manager", // or reject, return_for_changes
  "comments": "Final approval granted"
}
```

## 🎨 Frontend Integration

### **Button States Response:**
```json
{
  "ui": {
    "buttons": {
      "submitToBranchManager": {
        "visible": true,
        "enabled": true,
        "text": "Send to Branch Manager",
        "variant": "primary",
        "icon": "send"
      },
      "approve": {
        "visible": true,
        "enabled": false, // disabled if amount > threshold for BM
        "text": "Approve Application",
        "variant": "success",
        "icon": "check-circle"
      },
      "forwardToGM": {
        "visible": true,
        "enabled": true,
        "text": "Send to General Manager",
        "variant": "warning",
        "icon": "arrow-up",
        "required": true // if amount > threshold
      },
      "amountWarning": {
        "show": true,
        "message": "This loan amount exceeds your approval limit",
        "type": "warning"
      }
    },
    "progress": [
      {
        "id": 1,
        "title": "Application Created",
        "status": "completed",
        "icon": "file-plus"
      },
      // ... more steps
    ],
    "notifications": [
      {
        "type": "warning",
        "title": "High Value Loan",
        "message": "This loan requires GM approval",
        "priority": "medium"
      }
    ]
  }
}
```

## 🔒 Security & Authorization

### **Role-Based Access:**
- Users can only perform actions allowed for their role
- Branch Managers can only review applications from their branch
- Deputy Managers can only modify their own applications

### **Amount-Based Validation:**
- System prevents BM from approving loans > ₹25 Lakhs
- Automatic routing suggestions based on loan amount
- Clear error messages for invalid actions

## 📊 Workflow History & Audit Trail

Every action is logged with:
- Action performed
- User who performed it
- Timestamp
- Comments
- Status transition
- Loan amount at time of action

## 🎯 Frontend Implementation Guide

### **1. Display Current Status:**
```javascript
// Use the status indicator from API response
const { statusIndicator } = response.data.ui.buttons;
// statusIndicator.text, statusIndicator.color, statusIndicator.icon
```

### **2. Render Action Buttons:**
```javascript
// Loop through button states
Object.entries(response.data.ui.buttons).forEach(([key, button]) => {
  if (button.visible) {
    // Render button with button.text, button.variant, button.enabled
    // Show warning if button.required is true
  }
});
```

### **3. Show Progress Steps:**
```javascript
// Render progress indicator
response.data.ui.progress.forEach(step => {
  // step.status: 'completed', 'current', 'pending', 'rejected'
  // step.title, step.description, step.icon
});
```

### **4. Handle Amount Warnings:**
```javascript
if (response.data.ui.buttons.amountWarning?.show) {
  // Display warning message
  // Highlight required actions
}
```

## 🧪 Testing the Workflow

### **Test Scenarios:**

1. **Low Amount Loan (≤25L):**
   - Deputy Manager creates → submits
   - Branch Manager can approve directly

2. **High Amount Loan (>25L):**
   - Deputy Manager creates → submits  
   - Branch Manager must forward to GM
   - General Manager gives final approval

3. **Rejection Flow:**
   - Any approver can reject with comments
   - Application moves to rejected state

4. **Return for Changes:**
   - Any approver can return to Deputy Manager
   - Deputy Manager can modify and resubmit

## 🎉 Benefits

✅ **Enforced Hierarchy** - Clear maker-checker separation  
✅ **Amount-Based Routing** - Automatic escalation for high-value loans  
✅ **Audit Trail** - Complete history of all actions  
✅ **Frontend Ready** - Rich UI state information  
✅ **Security** - Role-based authorization at every step  
✅ **Flexibility** - Easy to modify thresholds and add new roles  

## 🔧 Configuration

### **Modify Loan Threshold:**
```javascript
// In models/loanWorkflow.model.js
const LOAN_AMOUNT_THRESHOLD = 2500000; // Change this value
```

### **Add New Roles:**
1. Update `WORKFLOW_TRANSITIONS` in the model
2. Add role to middleware validation
3. Update frontend button logic

This workflow system provides a robust, scalable foundation for loan approval processes with excellent frontend integration capabilities! 🚀
