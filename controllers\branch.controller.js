const Branch = require("../schema/branch.model");
const { paginateModel } = require("../Utilss/common");
const { softDeleteById } = require("../utils/index");

const createBranch = async (req, res) => {
  try {
    if (req.user.role === "super_admin") {
      console.log("Creating branch with data:", req.body);
      const branch = new Branch(req.body);
      await branch.save();
      res.json({ message: "Branch created successfully", branch });
    } else {
      res.status(403).json({ error: "Unauthorized" });
    }
  } catch (err) {
    console.error("Error creating branch:", err);

    // Handle specific MongoDB errors
    if (err.code === 11000) {
      const field = Object.keys(err.keyPattern)[0];
      return res.status(400).json({
        error: "Duplicate value",
        message: `A branch with this ${field} already exists`,
        field: field,
        value: err.keyValue[field]
      });
    }

    // Handle validation errors
    if (err.name === 'ValidationError') {
      const errors = {};
      Object.keys(err.errors).forEach(key => {
        errors[key] = err.errors[key].message;
      });
      return res.status(400).json({
        error: "Validation failed",
        details: errors
      });
    }

    // Generic error
    res.status(500).json({
      error: "Internal server error",
      message: err.message,
      details: process.env.NODE_ENV === 'development' ? err.stack : undefined
    });
  }
};

const deleteBranch = async (req, res) => {
  try {
    if (req.user.role === "superadmin") {
      const deletedBranch = await softDeleteById(Branch, req.params.id);

      if (!deletedBranch) {
        return res
          .status(404)
          .json({ message: "Branch not found or already deleted" });
      }

      res.json({
        message: "Branch soft-deleted successfully",
        data: deletedBranch,
      });
    } else {
      res.status(403).json({ error: "Unauthorized" });
    }
  } catch (error) {
    console.error("Error deleting branch:", error);
    res.status(500).json({ message: error.message });
  }
};

const getBranches = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const { role, branchId } = req.user;

    // Superadmins can view all branches
    // Employees can only view their assigned branch
    const filter = role === 'superadmin' || role === 'super_admin' ? { isDeleted: false } : { _id: branchId, isDeleted: false };

    // Debug: Check total count first
    const totalCount = await Branch.countDocuments(filter);
    console.log("Total branches in database:", totalCount);
    console.log("User role:", role);
    console.log("Filter:", filter);

    const result = await paginateModel(Branch, {
      page,
      limit,
      filter,
    });

    console.log("Branch pagination result:", result);

    // Transform the response to match expected format
    const response = {
      docs: result.data,
      total: result.totalItems,
      limit: limit,
      page: result.currentPage,
      totalPages: result.totalPages,
      hasNextPage: result.currentPage < result.totalPages,
      hasPreviousPage: result.currentPage > 1,
      nextPage: result.currentPage < result.totalPages ? result.currentPage + 1 : null,
      prevPage: result.currentPage > 1 ? result.currentPage - 1 : null
    };

    res.json(response);
  } catch (error) {
    console.error("Error getting branches:", error.message);
    res.status(500).json({ success: false, message: "Internal server error" });
  }
};

const getBranchById = async (req, res) => {
  try {
    const { role, branchId } = req.user;
    const branch = await Branch.findOne({ _id: req.params.id, isDeleted: false });

    if (!branch) {
      return res.status(404).json({ message: 'Branch not found' });
    }

    // Superadmins can view any branch
    // Employees can only view their assigned branch
    if (role === 'employee' && branch._id.toString() !== branchId) {
      return res.status(403).json({ error: 'Unauthorized - You can only view your assigned branch' });
    }

    res.json(branch);
  } catch (error) {
    console.error('Error fetching branch:', error);
    res.status(500).json({
      success: false,
      message: error.message,
    });
  }
};

const updateBranch = async (req, res) => {
  try {
    if (req.user.role !== 'superadmin') {
      return res.status(403).json({ error: 'Unauthorized - Only superadmins can update branches' });
    }

    const { id } = req.params;
    const updates = req.body;

    // Find and update the branch
    const branch = await Branch.findByIdAndUpdate(
      id,
      { $set: updates },
      { new: true, runValidators: true }
    );

    if (!branch) {
      return res.status(404).json({ message: 'Branch not found' });
    }

    res.json({
      success: true,
      message: 'Branch updated successfully',
      data: branch
    });
  } catch (error) {
    console.error('Error updating branch:', error);
    if (error.name === 'ValidationError') {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        error: error.message
      });
    }
    res.status(500).json({
      success: false,
      message: 'Error updating branch',
      error: error.message
    });
  }
};

module.exports = {
  createBranch,
  getBranches,
  deleteBranch,
  getBranchById,
  updateBranch
};
