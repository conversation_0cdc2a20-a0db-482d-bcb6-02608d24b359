const mongoose = require("mongoose");
const Schema = mongoose.Schema;

const CountrySchema = new Schema({
  name: { type: String, required: true },
  code: { type: String, required: true, unique: true },
  currency: { type: String, required: true },
  currencySymbol: { type: String, required: true },
  timeZone: { type: String, required: true },
  phoneCode: { type: String, required: true },
});

module.exports = mongoose.model("Country", CountrySchema);
