/**
 * Base Error class for application-specific errors
 */
class AppError extends Error {
    constructor(message, statusCode, details = null) {
        super(message);
        this.statusCode = statusCode || 500;
        this.status = `${statusCode}`.startsWith('4') ? 'fail' : 'error';
        this.isOperational = true;
        this.details = details;
        Error.captureStackTrace(this, this.constructor);
    }
}

/**
 * Custom error handler class
 * Extends the base AppError class for consistent error handling
 */
class ErrorHandler extends AppError {
    /**
     * Create a new ErrorHandler instance
     * @param {string} message - Error message
     * @param {number} statusCode - HTTP status code
     * @param {Object} details - Additional error details
     */
    constructor(message, statusCode = 500, details = null) {
        super(message, statusCode, details);
    }
}

/**
 * Validation Error - For input validation failures
 */
class ValidationError extends AppError {
    constructor(errors) {
        super('Validation failed', 400, errors);
    }
}

/**
 * Authentication Error - For authentication failures
 */
class AuthenticationError extends AppError {
    constructor(message = 'Authentication failed') {
        super(message, 401);
    }
}

/**
 * Authorization Error - For permission/role-based access failures
 */
class AuthorizationError extends AppError {
    constructor(message = 'Insufficient permissions') {
        super(message, 403);
    }
}

/**
 * Not Found Error - For resource not found scenarios
 */
class NotFoundError extends AppError {
    constructor(resource) {
        super(`${resource || 'Resource'} not found`, 404);
    }
}

/**
 * Conflict Error - For duplicate or conflicting data
 */
class ConflictError extends AppError {
    constructor(message = 'Resource already exists') {
        super(message, 409);
    }
}

/**
 * Rate Limit Error - For rate limiting scenarios
 */
class RateLimitError extends AppError {
    constructor(message = 'Too many requests, please try again later') {
        super(message, 429);
    }
}

/**
 * Log error information
 * @param {Error} err - Error object
 * @param {Object} req - Express request object
 */
const logError = (err, req) => {
    const errorInfo = {
        timestamp: new Date().toISOString(),
        method: req.method,
        path: req.path,
        params: req.params,
        query: req.query,
        body: req.body,
        error: {
            name: err.name,
            message: err.message,
            stack: process.env.NODE_ENV === 'development' ? err.stack : undefined,
            ...(err.details && { details: err.details })
        },
        user: req.user ? {
            id: req.user.userId,
            role: req.user.role,
            email: req.user.email
        } : 'unauthenticated'
    };

    // Use appropriate logging level based on status code
    if (err.statusCode >= 500) {
        console.error('Server Error:', JSON.stringify(errorInfo, null, 2));
    } else if (err.statusCode >= 400) {
        console.warn('Client Error:', JSON.stringify(errorInfo, null, 2));
    } else {
        console.log('Application Error:', JSON.stringify(errorInfo, null, 2));
    }
};

/**
 * Format error response
 * @param {Error} err - Error object
 * @param {Object} req - Express request object
 * @returns {Object} Formatted error response
 */
const formatErrorResponse = (err, req) => {
    const response = {
        status: err.status || 'error',
        message: err.message || 'Something went wrong',
        ...(process.env.NODE_ENV === 'development' && { stack: err.stack })
    };

    // Add validation errors if available
    if (err.details) {
        response.errors = err.details;
    }

    // Add request ID if available
    if (req.id) {
        response.requestId = req.id;
    }

    return response;
};

/**
 * Global error handling middleware
 * @param {Error} err - Error object
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const globalErrorHandler = (err, req, res, next) => {
    // Set default status code if not set
    err.statusCode = err.statusCode || 500;
    err.status = err.status || 'error';

    // Log the error
    logError(err, req);

    // Handle specific error types
    if (err.name === 'ValidationError' || err.name === 'ValidatorError') {
        // Mongoose validation error
        const errors = {};
        Object.keys(err.errors).forEach(key => {
            errors[key] = err.errors[key].message;
        });
        err = new ValidationError(errors);
    } else if (err.name === 'JsonWebTokenError') {
        // JWT error
        err = new AuthenticationError('Invalid token. Please log in again!');
    } else if (err.name === 'TokenExpiredError') {
        // JWT expired
        err = new AuthenticationError('Your token has expired! Please log in again.');
    } else if (err.code === 11000) {
        // MongoDB duplicate key error
        const field = Object.keys(err.keyValue)[0];
        err = new ConflictError(`A resource with this ${field} already exists`);
    } else if (err.name === 'CastError') {
        // Invalid MongoDB ID
        err = new ErrorHandler(`Invalid ${err.path}: ${err.value}`, 400);
    } else if (err.type === 'entity.too.large') {
        // Request entity too large
        err = new ErrorHandler('Request payload is too large', 413);
    }

    // Format the error response
    const errorResponse = formatErrorResponse(err, req);

    // Send error response
    res.status(err.statusCode).json(errorResponse);
};

// Export all error classes and the global error handler
module.exports = {
    // Base error classes
    AppError,
    ErrorHandler,
    
    // Specific error types
    ValidationError,
    AuthenticationError,
    AuthorizationError,
    NotFoundError,
    ConflictError,
    RateLimitError,
    
    // Middleware
    globalErrorHandler
};
