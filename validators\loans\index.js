const housingLoanValidator = require('./housing-loan.validator');
// Import other loan validators as they are created
// const vehicleLoanValidator = require('./vehicle-loan.validator');
// const personalLoanValidator = require('./personal-loan.validator');

// Map of loan types to their respective validators
const loanValidators = {
  housing: housingLoanValidator,
  // vehicle: vehicleLoanValidator,
  // personal: personalLoanValidator
};

/**
 * Get the appropriate validator for a loan type
 * @param {string} loanType - Type of loan (e.g., 'housing', 'vehicle')
 * @returns {Object} The validator object for the specified loan type
 */
const getLoanValidator = (loanType) => {
  const validator = loanValidators[loanType];
  if (!validator) {
    throw new Error(`No validator found for loan type: ${loanType}`);
  }
  return validator;
};

/**
 * Middleware to validate loan data based on loan type
 */
const validateLoan = (req, res, next) => {
  try {
    const { loanType } = req.body.applicationInfo || {};
    const validator = getLoanValidator(loanType);
    return validator.validateHousingLoan(req, res, next);
  } catch (error) {
    return next(error);
  }
};

/**
 * Middleware to validate loan update data based on loan type
 */
const validateLoanUpdate = (req, res, next) => {
  try {
    const { loanType } = req.body.applicationInfo || req.params || {};
    const validator = getLoanValidator(loanType);
    return validator.validateHousingLoanUpdate(req, res, next);
  } catch (error) {
    return next(error);
  }
};

module.exports = {
  // Individual validators
  housingLoanValidator,
  // vehicleLoanValidator,
  // personalLoanValidator,
  
  // Utility functions
  getLoanValidator,
  validateLoan,
  validateLoanUpdate,
  
  // All validators map
  loanValidators
};
