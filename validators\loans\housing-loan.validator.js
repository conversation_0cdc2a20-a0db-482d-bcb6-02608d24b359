const Joi = require('joi');
const { baseLoanSchema, validateBaseLoan } = require('../base-loan.validator');
const { addressSchema, commonPatterns } = require('../schemas/common.schemas');

// Extend the base loan schema with housing loan specific fields
const housingLoanSchema = baseLoanSchema.keys({
  // Override loan type to be specifically 'housing'
  applicationInfo: baseLoanSchema.extract('applicationInfo').keys({
    loanType: Joi.string().valid('housing').default('housing')
  }),

  // Housing loan specific details
  loanDetails: Joi.object({
    purpose: Joi.string()
      .valid('purchase', 'construction', 'extension', 'repair', 'takeover', 'plot')
      .required()
      .messages({ 'any.required': 'Loan purpose is required' }),
      
    amount: commonPatterns.amount.required()
      .messages({ 'any.required': 'Loan amount is required' }),
      
    tenure: Joi.number().integer().min(1).max(30) // in years
      .required()
      .messages({ 'any.required': 'Loan tenure is required' }),
      
    propertyValue: commonPatterns.amount.required()
      .messages({ 'any.required': 'Property value is required' }),
      
    ltvRatio: Joi.number().min(0).max(90) // Loan-to-Value ratio
      .messages({ 'number.max': 'LTV ratio cannot exceed 90%' })
  }).required(),

  // Property details
  propertyDetails: Joi.object({
    address: addressSchema.required(),
    type: Joi.string()
      .valid('residential', 'commercial', 'plot', 'under_construction')
      .required(),
    area: Joi.number().min(0).required(), // in sq.ft
    builtUpArea: Joi.number().min(0).required(),
    ownershipType: Joi.string()
      .valid('freehold', 'leasehold', 'power_of_attorney')
      .required(),
    ageOfProperty: Joi.number().min(0),
    builderDetails: Joi.object({
      name: Joi.string().required(),
      reraNumber: Joi.string(),
      contact: Joi.string().pattern(/^[0-9]{10}$/)
    }).when('type', {
      is: 'under_construction',
      then: Joi.required()
    })
  }).required(),

  // Insurance details (optional)
  insurance: Joi.object({
    isRequired: Joi.boolean().default(true),
    provider: Joi.string(),
    policyNumber: Joi.string(),
    amount: commonPatterns.amount,
    startDate: Joi.date(),
    endDate: Joi.date().min(Joi.ref('startDate'))
  })
});

// Validation middleware for creating a new housing loan
const validateHousingLoan = (req, res, next) => {
  // First validate base loan fields
  return validateBaseLoan(req, res, (err) => {
    if (err) return next(err);
    
    // Then validate housing loan specific fields
    return validate(housingLoanSchema, 'body')(req, res, next);
  });
};

// Validation middleware for updating a housing loan
const validateHousingLoanUpdate = (req, res, next) => {
  const updateSchema = housingLoanSchema.fork(
    ['applicationInfo', 'loanDetails', 'propertyDetails', 'insurance'],
    (schema) => schema.optional()
  );
  
  return validate(updateSchema, 'body')(req, res, next);
};

module.exports = {
  housingLoanSchema,
  validateHousingLoan,
  validateHousingLoanUpdate
};
