/**
 * Frontend to Backend Field Mapping for Vehicle Loan
 * This ensures your frontend field names map correctly to backend processing
 */

const VEHICLE_LOAN_FIELD_MAPPING = {
  // Applicant Details
  'applicantName': {
    backendField: 'applicantName',
    primaryApplicationPath: 'personalInfo.fname',
    pdfVariable: 'applicant_name',
    validation: { required: true, minLength: 2, maxLength: 100 }
  },
  'fatherName': {
    backendField: 'fatherName',
    primaryApplicationPath: 'personalInfo.fatherName',
    pdfVariable: 'father_name',
    validation: { required: true, minLength: 2, maxLength: 100 }
  },
  'dateOfBirth': {
    backendField: 'dateOfBirth',
    primaryApplicationPath: 'personalInfo.dateOfBirth',
    pdfVariable: 'date_of_birth',
    formatter: 'date',
    validation: { required: true }
  },
  'age': {
    backendField: 'age',
    primaryApplicationPath: 'personalInfo.age',
    pdfVariable: 'applicant_age',
    validation: { required: true, min: 18, max: 70 }
  },
  'gender': {
    backendField: 'gender',
    primaryApplicationPath: 'personalInfo.gender',
    pdfVariable: 'applicant_gender',
    formatter: 'capitalize',
    validation: { required: true }
  },
  'maritalStatus': {
    backendField: 'maritalStatus',
    primaryApplicationPath: 'personalInfo.maritalStatus',
    pdfVariable: 'marital_status',
    formatter: 'capitalize',
    validation: { required: true }
  },
  'mobileNumber': {
    backendField: 'mobileNumber',
    primaryApplicationPath: 'phone.number',
    pdfVariable: 'mobile_number',
    formatter: 'phone',
    validation: { required: true, pattern: '^[6-9]\\d{9}$' }
  },
  'email': {
    backendField: 'email',
    primaryApplicationPath: 'personalInfo.email',
    pdfVariable: 'email_address',
    validation: { required: true, type: 'email' }
  },
  'address': {
    backendField: 'address',
    primaryApplicationPath: 'homeAddress.fullAddress',
    pdfVariable: 'permanent_address',
    validation: { required: true, minLength: 10, maxLength: 500 }
  },

  // Vehicle Details
  'vehicleType': {
    backendField: 'vehicleType',
    primaryApplicationPath: 'loanSpecific.vehicleType',
    pdfVariable: 'vehicle_type',
    formatter: 'capitalize',
    validation: { required: true }
  },
  'vehicleMake': {
    backendField: 'vehicleMake',
    primaryApplicationPath: 'loanSpecific.vehicleMake',
    pdfVariable: 'vehicle_make',
    validation: { required: true }
  },
  'vehicleModel': {
    backendField: 'vehicleModel',
    primaryApplicationPath: 'loanSpecific.vehicleModel',
    pdfVariable: 'vehicle_model',
    validation: { required: true }
  },
  'vehicleVariant': {
    backendField: 'vehicleVariant',
    primaryApplicationPath: 'loanSpecific.vehicleVariant',
    pdfVariable: 'vehicle_variant',
    validation: { required: false }
  },
  'manufacturingYear': {
    backendField: 'manufacturingYear',
    primaryApplicationPath: 'loanSpecific.manufacturingYear',
    pdfVariable: 'manufacturing_year',
    validation: { required: true, min: 2015, max: 2025 }
  },
  'vehicleCondition': {
    backendField: 'vehicleCondition',
    primaryApplicationPath: 'loanSpecific.vehicleCondition',
    pdfVariable: 'vehicle_condition',
    formatter: 'capitalize',
    validation: { required: true }
  },
  'vehiclePrice': {
    backendField: 'vehiclePrice',
    primaryApplicationPath: 'loanSpecific.vehiclePrice',
    pdfVariable: 'vehicle_price',
    formatter: 'currency',
    validation: { required: true, min: 50000 }
  },
  'dealerName': {
    backendField: 'dealerName',
    primaryApplicationPath: 'loanSpecific.dealerName',
    pdfVariable: 'dealer_name',
    validation: { required: true }
  },

  // Loan Details
  'loanAmount': {
    backendField: 'loanAmount',
    primaryApplicationPath: 'loanInfo.amount',
    pdfVariable: 'loan_amount',
    formatter: 'currency',
    validation: { required: true, min: 50000, max: 5000000 }
  },
  'downPayment': {
    backendField: 'downPayment',
    primaryApplicationPath: 'loanSpecific.downPayment',
    pdfVariable: 'down_payment',
    formatter: 'currency',
    validation: { required: true }
  },
  'loanTenure': {
    backendField: 'loanTenure',
    primaryApplicationPath: 'loanInfo.tenure',
    pdfVariable: 'loan_tenure',
    validation: { required: true }
  },
  'interestType': {
    backendField: 'interestType',
    primaryApplicationPath: 'loanInfo.interestType',
    pdfVariable: 'interest_type',
    formatter: 'capitalize',
    validation: { required: true }
  },

  // Income Details
  'employmentType': {
    backendField: 'employmentType',
    primaryApplicationPath: 'occupation.type',
    pdfVariable: 'employment_type',
    formatter: 'capitalize',
    validation: { required: true }
  },
  'monthlyIncome': {
    backendField: 'monthlyIncome',
    primaryApplicationPath: 'occupation.monthlyIncome',
    pdfVariable: 'monthly_income',
    formatter: 'currency',
    validation: { required: true, min: 15000 }
  },
  'companyName': {
    backendField: 'companyName',
    primaryApplicationPath: 'occupation.companyName',
    pdfVariable: 'company_name',
    validation: { required: true }
  },
  'workExperience': {
    backendField: 'workExperience',
    primaryApplicationPath: 'occupation.workExperience',
    pdfVariable: 'work_experience',
    validation: { required: true, min: 0, max: 50 }
  },
  'officeAddress': {
    backendField: 'officeAddress',
    primaryApplicationPath: 'businessAddress.fullAddress',
    pdfVariable: 'office_address',
    validation: { required: true }
  },

  // Financial Details
  'existingLoans': {
    backendField: 'existingLoans',
    primaryApplicationPath: 'financialInfo.existingLoans',
    pdfVariable: 'existing_loans',
    formatter: 'currency',
    validation: { required: false }
  },
  'bankAccount': {
    backendField: 'bankAccount',
    primaryApplicationPath: 'financialInfo.bankAccountNumber',
    pdfVariable: 'bank_account',
    validation: { required: true }
  },
  'panNumber': {
    backendField: 'panNumber',
    primaryApplicationPath: 'personalInfo.panNumber',
    pdfVariable: 'pan_number',
    formatter: 'uppercase',
    validation: { required: true, pattern: '[A-Z]{5}[0-9]{4}[A-Z]{1}' }
  },
  'aadharNumber': {
    backendField: 'aadharNumber',
    primaryApplicationPath: 'personalInfo.aadharNumber',
    pdfVariable: 'aadhar_number',
    validation: { required: true, pattern: '[0-9]{4}\\s[0-9]{4}\\s[0-9]{4}' }
  },

  // Declaration
  'informationAccuracy': {
    backendField: 'informationAccuracy',
    primaryApplicationPath: 'declarations.informationAccuracy',
    pdfVariable: 'information_accuracy',
    validation: { required: true }
  }
};

/**
 * Transform frontend form data to backend format
 */
const transformFrontendToBackend = (frontendData) => {
  const backendData = {};
  
  Object.keys(frontendData).forEach(frontendKey => {
    const mapping = VEHICLE_LOAN_FIELD_MAPPING[frontendKey];
    if (mapping) {
      backendData[mapping.backendField] = frontendData[frontendKey];
    } else {
      // If no mapping found, use the key as-is
      backendData[frontendKey] = frontendData[frontendKey];
    }
  });
  
  return backendData;
};

/**
 * Generate primary application data from frontend form data
 */
const generatePrimaryApplicationData = (frontendData) => {
  const primaryAppData = {};
  
  Object.keys(frontendData).forEach(frontendKey => {
    const mapping = VEHICLE_LOAN_FIELD_MAPPING[frontendKey];
    if (mapping && mapping.primaryApplicationPath) {
      setNestedProperty(primaryAppData, mapping.primaryApplicationPath, frontendData[frontendKey]);
    }
  });
  
  return primaryAppData;
};

/**
 * Generate PDF template data from frontend form data
 */
const generatePDFTemplateData = (frontendData) => {
  const pdfData = {};
  
  Object.keys(frontendData).forEach(frontendKey => {
    const mapping = VEHICLE_LOAN_FIELD_MAPPING[frontendKey];
    if (mapping && mapping.pdfVariable) {
      let value = frontendData[frontendKey];
      
      // Apply formatter if specified
      if (mapping.formatter) {
        value = applyFormatter(mapping.formatter, value);
      }
      
      pdfData[mapping.pdfVariable] = value;
    }
  });
  
  // Add metadata
  pdfData.generated_at = new Date().toLocaleString();
  pdfData.loan_type = 'Vehicle Loan';
  
  return pdfData;
};

/**
 * Validate frontend form data
 */
const validateFrontendData = (frontendData) => {
  const errors = [];
  
  Object.keys(frontendData).forEach(frontendKey => {
    const mapping = VEHICLE_LOAN_FIELD_MAPPING[frontendKey];
    if (mapping && mapping.validation) {
      const value = frontendData[frontendKey];
      const validation = mapping.validation;
      
      // Required field validation
      if (validation.required && (!value || value === '')) {
        errors.push({
          field: frontendKey,
          message: `${frontendKey} is required`
        });
      }
      
      // Type and pattern validation
      if (value && validation.pattern) {
        const regex = new RegExp(validation.pattern);
        if (!regex.test(value)) {
          errors.push({
            field: frontendKey,
            message: `${frontendKey} format is invalid`
          });
        }
      }
      
      // Range validation
      if (value && validation.min && Number(value) < validation.min) {
        errors.push({
          field: frontendKey,
          message: `${frontendKey} must be at least ${validation.min}`
        });
      }
      
      if (value && validation.max && Number(value) > validation.max) {
        errors.push({
          field: frontendKey,
          message: `${frontendKey} must not exceed ${validation.max}`
        });
      }
    }
  });
  
  return errors;
};

/**
 * Helper function to set nested properties
 */
const setNestedProperty = (obj, path, value) => {
  const keys = path.split('.');
  let current = obj;
  
  for (let i = 0; i < keys.length - 1; i++) {
    const key = keys[i];
    if (!(key in current)) {
      current[key] = {};
    }
    current = current[key];
  }
  
  current[keys[keys.length - 1]] = value;
};

/**
 * Apply formatters to values
 */
const applyFormatter = (formatterName, value) => {
  const formatters = {
    currency: (val) => `₹${Number(val).toLocaleString('en-IN')}`,
    date: (val) => new Date(val).toLocaleDateString('en-IN'),
    phone: (val) => val.toString().replace(/(\d{3})(\d{3})(\d{4})/, '$1-$2-$3'),
    uppercase: (val) => val.toString().toUpperCase(),
    capitalize: (val) => val.toString().charAt(0).toUpperCase() + val.toString().slice(1)
  };
  
  return formatters[formatterName] ? formatters[formatterName](value) : value;
};

module.exports = {
  VEHICLE_LOAN_FIELD_MAPPING,
  transformFrontendToBackend,
  generatePrimaryApplicationData,
  generatePDFTemplateData,
  validateFrontendData
};
