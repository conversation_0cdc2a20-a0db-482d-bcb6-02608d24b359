const jwt = require('jsonwebtoken');
const { ErrorHand<PERSON> } = require('../utils/errorHandler');

/**
 * Middleware to authenticate JWT token
 */
const authMiddleware = (req, res, next) => {
    let token;
    const authHeader = req.headers.authorization;

    // Extract token from Authorization header or cookies
    if (authHeader && authHeader.startsWith('Bearer ')) {
        token = authHeader.split(' ')[1];
    } else if (req.cookies?.token) {
        token = req.cookies.token;
    }

    if (!token) {
        return next(new ErrorHandler('No authentication token provided', 401));
    }

    try {
        // Verify and decode the token
        const decoded = jwt.verify(token, process.env.JWT_SECRET);

        // Attach user info to request object
        req.user = {
            userId: decoded.userId,
            email: decoded.email,
            role: decoded.role,
            permissions: decoded.permissions || []
        };

        next();
    } catch (err) {
        if (err.name === 'TokenExpiredError') {
            return next(new <PERSON><PERSON>r<PERSON>and<PERSON>('Token has expired', 401));
        }
        return next(new <PERSON><PERSON>r<PERSON><PERSON><PERSON>('Invalid authentication token', 401));
    }
};

/**
 * Middleware to check if user has required role
 * @param {Array} allowedRoles - Array of allowed roles
 */
const roleMiddleware = (allowedRoles) => {
    return (req, res, next) => {
        if (!req.user) {
            return next(new ErrorHandler('Authentication required', 401));
        }

        if (!allowedRoles.includes(req.user.role)) {
            return next(new ErrorHandler('Insufficient permissions', 403));
        }

        next();
    };
};

/**
 * Middleware to check if user has required permission
 * @param {string} permission - Required permission
 * @param {Object} options - Additional options
 * @param {boolean} options.allowSelf - Allow access if user is accessing their own resource
 * @param {string} options.idParam - Name of the ID parameter to check against userId
 */
const permissionMiddleware = (permission, options = {}) => {
    return (req, res, next) => {
        const { allowSelf = false, idParam = 'id' } = options;

        // Super admin and General Manager have all permissions
        if (req.user.role === 'super_admin' || req.user.role === 'general_manager') {
            return next();
        }

        // Check if user has the required permission
        const hasPermission = req.user.permissions.includes('*') ||
            req.user.permissions.includes(permission);

        if (!hasPermission) {
            // Check for ownership if allowSelf is true
            if (allowSelf && req.params[idParam] === req.user.userId.toString()) {
                return next();
            }
            return next(new ErrorHandler('Insufficient permissions', 403));
        }

        next();
    };
};

/**
 * Middleware to get current user info
 */
const currentUserMiddleware = (req, res, next) => {
    if (!req.user) {
        return next(new ErrorHandler('Not authenticated', 401));
    }

    // Attach user info to response locals for use in templates if needed
    res.locals.user = req.user;
    next();
};

module.exports = {
    authMiddleware,
    roleMiddleware,
    permissionMiddleware,
    currentUserMiddleware
};