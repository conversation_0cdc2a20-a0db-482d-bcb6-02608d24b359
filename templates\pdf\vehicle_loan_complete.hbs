<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Vehicle Loan Application</title>
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            margin: 0; 
            padding: 20px; 
            line-height: 1.5; 
            color: #333;
            background-color: #fff;
        }
        
        .header { 
            text-align: center; 
            border-bottom: 3px solid #2c3e50; 
            padding-bottom: 25px; 
            margin-bottom: 35px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 25px;
            border-radius: 8px;
        }
        
        .header h1 {
            color: #2c3e50;
            font-size: 28px;
            margin: 0 0 15px 0;
            font-weight: 700;
        }
        
        .header p {
            margin: 5px 0;
            font-size: 14px;
            color: #6c757d;
        }
        
        .section { 
            margin-bottom: 30px; 
            page-break-inside: avoid;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .section-title { 
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 15px 20px; 
            font-weight: 600; 
            margin: 0;
            font-size: 16px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .section-content {
            padding: 20px;
        }
        
        .field-row {
            display: flex;
            margin-bottom: 12px;
            align-items: flex-start;
            page-break-inside: avoid;
        }

        .field-label {
            width: 180px;
            font-weight: 600;
            color: #495057;
            flex-shrink: 0;
            padding-right: 10px;
        }

        .field-value {
            flex: 1;
            color: #212529;
            padding-left: 10px;
            border-left: 2px solid #e9ecef;
            word-wrap: break-word;
            overflow-wrap: break-word;
        }
        
        .grid {
            display: block;
            margin-bottom: 20px;
        }

        .grid-row {
            display: flex;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }

        .grid-col {
            flex: 1;
            min-width: 300px;
            margin-right: 20px;
        }

        .grid-col:last-child {
            margin-right: 0;
        }

        .full-width {
            width: 100%;
        }
        
        .signature-section { 
            margin-top: 60px; 
            display: flex; 
            justify-content: space-between;
            page-break-inside: avoid;
        }
        
        .signature-box { 
            width: 200px; 
            text-align: center; 
            border-top: 2px solid #495057; 
            padding-top: 15px;
            font-weight: 600;
            color: #495057;
        }
        
        .amount-highlight {
            background-color: #fff3cd;
            padding: 3px 8px;
            border-radius: 4px;
            font-weight: 600;
            color: #856404;
        }
        
        .status-badge {
            background-color: #d4edda;
            color: #155724;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
            text-align: center;
            font-size: 12px;
            color: #6c757d;
        }
        
        @media print {
            body { margin: 0; }
            .section { page-break-inside: avoid; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>VEHICLE LOAN APPLICATION</h1>
        <p><strong>Application ID:</strong> {{application_id}}</p>
        <p><strong>Generated On:</strong> {{generated_at}}</p>
        <p><strong>Loan Type:</strong> {{loan_type}}</p>
        <span class="status-badge">{{#if status}}{{status}}{{else}}Draft{{/if}}</span>
    </div>

    <div class="section">
        <div class="section-title">Applicant Personal Information</div>
        <div class="section-content">
            <div class="grid">
                <div class="grid-row">
                    <div class="grid-col">
                        <div class="field-row">
                            <div class="field-label">Full Name:</div>
                            <div class="field-value">{{applicant_name}}</div>
                        </div>
                        <div class="field-row">
                            <div class="field-label">Father's Name:</div>
                            <div class="field-value">{{father_name}}</div>
                        </div>
                        <div class="field-row">
                            <div class="field-label">Date of Birth:</div>
                            <div class="field-value">{{date_of_birth}}</div>
                        </div>
                        <div class="field-row">
                            <div class="field-label">Age:</div>
                            <div class="field-value">{{applicant_age}} years</div>
                        </div>
                    </div>
                    <div class="grid-col">
                        <div class="field-row">
                            <div class="field-label">Gender:</div>
                            <div class="field-value">{{applicant_gender}}</div>
                        </div>
                        <div class="field-row">
                            <div class="field-label">Marital Status:</div>
                            <div class="field-value">{{marital_status}}</div>
                        </div>
                        <div class="field-row">
                            <div class="field-label">Mobile Number:</div>
                            <div class="field-value">{{mobile_number}}</div>
                        </div>
                        <div class="field-row">
                            <div class="field-label">Email Address:</div>
                            <div class="field-value">{{email_address}}</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="field-row full-width">
                <div class="field-label">Permanent Address:</div>
                <div class="field-value">{{permanent_address}}</div>
            </div>
        </div>
    </div>

    <div class="section">
        <div class="section-title">Vehicle Information</div>
        <div class="section-content">
            <div class="grid">
                <div class="grid-row">
                    <div class="grid-col">
                        <div class="field-row">
                            <div class="field-label">Vehicle Type:</div>
                            <div class="field-value">{{vehicle_type}}</div>
                        </div>
                        <div class="field-row">
                            <div class="field-label">Make/Brand:</div>
                            <div class="field-value">{{vehicle_make}}</div>
                        </div>
                        <div class="field-row">
                            <div class="field-label">Model:</div>
                            <div class="field-value">{{vehicle_model}}</div>
                        </div>
                        <div class="field-row">
                            <div class="field-label">Variant:</div>
                            <div class="field-value">{{vehicle_variant}}</div>
                        </div>
                    </div>
                    <div class="grid-col">
                        <div class="field-row">
                            <div class="field-label">Manufacturing Year:</div>
                            <div class="field-value">{{manufacturing_year}}</div>
                        </div>
                        <div class="field-row">
                            <div class="field-label">Condition:</div>
                            <div class="field-value">{{vehicle_condition}}</div>
                        </div>
                        <div class="field-row">
                            <div class="field-label">Vehicle Price:</div>
                            <div class="field-value"><span class="amount-highlight">{{vehicle_price}}</span></div>
                        </div>
                        <div class="field-row">
                            <div class="field-label">Dealer/Seller:</div>
                            <div class="field-value">{{dealer_name}}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="section">
        <div class="section-title">Loan Requirements</div>
        <div class="section-content">
            <div class="grid">
                <div class="grid-row">
                    <div class="grid-col">
                        <div class="field-row">
                            <div class="field-label">Loan Amount:</div>
                            <div class="field-value"><span class="amount-highlight">{{loan_amount}}</span></div>
                        </div>
                        <div class="field-row">
                            <div class="field-label">Down Payment:</div>
                            <div class="field-value"><span class="amount-highlight">{{down_payment}}</span></div>
                        </div>
                    </div>
                    <div class="grid-col">
                        <div class="field-row">
                            <div class="field-label">Loan Tenure:</div>
                            <div class="field-value">{{loan_tenure}} years</div>
                        </div>
                        <div class="field-row">
                            <div class="field-label">Interest Type:</div>
                            <div class="field-value">{{interest_type}}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="section">
        <div class="section-title">Income & Employment Information</div>
        <div class="section-content">
            <div class="grid">
                <div class="grid-row">
                    <div class="grid-col">
                        <div class="field-row">
                            <div class="field-label">Employment Type:</div>
                            <div class="field-value">{{employment_type}}</div>
                        </div>
                        <div class="field-row">
                            <div class="field-label">Monthly Income:</div>
                            <div class="field-value"><span class="amount-highlight">{{monthly_income}}</span></div>
                        </div>
                    </div>
                    <div class="grid-col">
                        <div class="field-row">
                            <div class="field-label">Company Name:</div>
                            <div class="field-value">{{company_name}}</div>
                        </div>
                        <div class="field-row">
                            <div class="field-label">Work Experience:</div>
                            <div class="field-value">{{work_experience}} years</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="field-row full-width">
                <div class="field-label">Office Address:</div>
                <div class="field-value">{{office_address}}</div>
            </div>
        </div>
    </div>

    <div class="section">
        <div class="section-title">Financial Information</div>
        <div class="section-content">
            <div class="grid">
                <div class="grid-row">
                    <div class="grid-col">
                        <div class="field-row">
                            <div class="field-label">Existing Loan EMIs:</div>
                            <div class="field-value">{{#if existing_loans}}{{existing_loans}}{{else}}None{{/if}}</div>
                        </div>
                        <div class="field-row">
                            <div class="field-label">Primary Bank:</div>
                            <div class="field-value">{{bank_account}}</div>
                        </div>
                    </div>
                    <div class="grid-col">
                        <div class="field-row">
                            <div class="field-label">PAN Number:</div>
                            <div class="field-value">{{pan_number}}</div>
                        </div>
                        <div class="field-row">
                            <div class="field-label">Aadhar Number:</div>
                            <div class="field-value">{{aadhar_number}}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="section">
        <div class="section-title">Declaration</div>
        <div class="section-content">
            <div class="field-row">
                <div class="field-label">Applicant Declaration:</div>
                <div class="field-value">
                    {{#if information_accuracy}}
                        ✓ I/We declare that all information provided is true and accurate. I/We accept the terms and conditions of the vehicle loan. I/We commit to repay the loan as per the agreed schedule. I/We authorize the bank to verify all submitted documents.
                    {{else}}
                        Declaration not confirmed
                    {{/if}}
                </div>
            </div>
        </div>
    </div>

    <div class="signature-section">
        <div class="signature-box">
            <div>Applicant Signature</div>
            <div style="margin-top: 10px; font-size: 12px;">{{applicant_name}}</div>
        </div>
        <div class="signature-box">
            <div>Branch Manager</div>
            <div style="margin-top: 10px; font-size: 12px;">Approval</div>
        </div>
        <div class="signature-box">
            <div>Date</div>
            <div style="margin-top: 10px; font-size: 12px;">{{generated_at}}</div>
        </div>
    </div>

    <div class="footer">
        <p>This is a computer-generated document. No signature is required for validity.</p>
        <p>For any queries, please contact your nearest branch or call our customer service.</p>
    </div>
</body>
</html>
