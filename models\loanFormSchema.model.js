const mongoose = require('mongoose');

// Base field definition for dynamic forms
const FormFieldSchema = new mongoose.Schema({
  fieldName: {
    type: String,
    required: true,
    trim: true
  },
  fieldType: {
    type: String,
    enum: ['text', 'number', 'email', 'phone', 'date', 'select', 'radio', 'checkbox', 'textarea', 'file', 'currency', 'percentage'],
    required: true
  },
  label: {
    type: String,
    required: true,
    trim: true
  },
  placeholder: {
    type: String,
    trim: true
  },
  isRequired: {
    type: Boolean,
    default: false
  },
  validation: {
    minLength: Number,
    maxLength: Number,
    min: Number,
    max: Number,
    pattern: String, // Regex pattern
    customValidation: String // Custom validation function name
  },
  options: [{
    label: String,
    value: String
  }], // For select, radio, checkbox fields
  defaultValue: mongoose.Schema.Types.Mixed,
  section: {
    type: String,
    required: true,
    trim: true
  },
  order: {
    type: Number,
    default: 0
  },
  dependsOn: {
    field: String,
    value: mongoose.Schema.Types.Mixed,
    condition: {
      type: String,
      enum: ['equals', 'not_equals', 'greater_than', 'less_than', 'contains']
    }
  },
  // Mapping to primary application fields
  primaryApplicationMapping: {
    fieldPath: String, // e.g., 'personalDetails.firstName', 'loanDetails.amount'
    transformer: String // Function name to transform the value if needed
  },
  // PDF template mapping
  pdfMapping: {
    templateVariable: String, // Variable name in Handlebars template
    formatter: String // Function name to format value for PDF
  }
}, { _id: false });

// Section definition for organizing form fields
const FormSectionSchema = new mongoose.Schema({
  sectionName: {
    type: String,
    required: true,
    trim: true
  },
  title: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  order: {
    type: Number,
    default: 0
  },
  isCollapsible: {
    type: Boolean,
    default: false
  },
  isExpanded: {
    type: Boolean,
    default: true
  }
}, { _id: false });

// Main loan form schema
const LoanFormSchemaModel = new mongoose.Schema({
  loanType: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    enum: ['vehicle_loan', 'housing_loan', 'personal_loan', 'business_loan', 'education_loan', 'gold_loan']
  },
  loanTypeName: {
    type: String,
    required: true,
    trim: true
  },
  version: {
    type: String,
    default: '1.0.0'
  },
  description: {
    type: String,
    trim: true
  },
  sections: [FormSectionSchema],
  fields: [FormFieldSchema],
  
  // PDF template configuration
  pdfTemplate: {
    templateName: {
      type: String,
      required: true
    },
    templatePath: {
      type: String,
      required: true
    },
    orientation: {
      type: String,
      enum: ['portrait', 'landscape'],
      default: 'portrait'
    },
    format: {
      type: String,
      enum: ['A4', 'A3', 'Letter'],
      default: 'A4'
    },
    margins: {
      top: { type: String, default: '20mm' },
      right: { type: String, default: '20mm' },
      bottom: { type: String, default: '20mm' },
      left: { type: String, default: '20mm' }
    }
  },
  
  // Validation rules
  validationRules: {
    crossFieldValidations: [{
      rule: String, // Function name for cross-field validation
      fields: [String], // Fields involved in validation
      errorMessage: String
    }],
    businessRules: [{
      rule: String, // Function name for business rule validation
      errorMessage: String
    }]
  },
  
  // Workflow integration
  workflowConfig: {
    autoSubmitToBM: {
      type: Boolean,
      default: false
    },
    requiredDocuments: [String],
    approvalThresholds: {
      branchManager: Number,
      generalManager: Number
    }
  },
  
  isActive: {
    type: Boolean,
    default: true
  },
  
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  }
}, {
  timestamps: true
});

// Indexes for performance
LoanFormSchemaModel.index({ loanType: 1, isActive: 1 });
LoanFormSchemaModel.index({ 'fields.fieldName': 1 });

// Methods
LoanFormSchemaModel.methods.getFieldByName = function(fieldName) {
  return this.fields.find(field => field.fieldName === fieldName);
};

LoanFormSchemaModel.methods.getFieldsBySection = function(sectionName) {
  return this.fields.filter(field => field.section === sectionName)
                   .sort((a, b) => a.order - b.order);
};

LoanFormSchemaModel.methods.getSectionsOrdered = function() {
  return this.sections.sort((a, b) => a.order - b.order);
};

LoanFormSchemaModel.methods.validateFormData = function(formData) {
  const errors = [];
  
  // Validate required fields
  this.fields.forEach(field => {
    if (field.isRequired && (!formData[field.fieldName] || formData[field.fieldName] === '')) {
      errors.push({
        field: field.fieldName,
        message: `${field.label} is required`
      });
    }
  });
  
  // Validate field types and constraints
  this.fields.forEach(field => {
    const value = formData[field.fieldName];
    if (value !== undefined && value !== null && value !== '') {
      const fieldErrors = this.validateFieldValue(field, value);
      errors.push(...fieldErrors);
    }
  });
  
  return errors;
};

LoanFormSchemaModel.methods.validateFieldValue = function(field, value) {
  const errors = [];
  
  // Type validation
  switch (field.fieldType) {
    case 'number':
    case 'currency':
    case 'percentage':
      if (isNaN(value)) {
        errors.push({
          field: field.fieldName,
          message: `${field.label} must be a valid number`
        });
      }
      break;
    case 'email':
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(value)) {
        errors.push({
          field: field.fieldName,
          message: `${field.label} must be a valid email address`
        });
      }
      break;
    case 'phone':
      const phoneRegex = /^[6-9]\d{9}$/;
      if (!phoneRegex.test(value.toString().replace(/\D/g, ''))) {
        errors.push({
          field: field.fieldName,
          message: `${field.label} must be a valid 10-digit phone number`
        });
      }
      break;
  }
  
  // Length validation
  if (field.validation) {
    if (field.validation.minLength && value.toString().length < field.validation.minLength) {
      errors.push({
        field: field.fieldName,
        message: `${field.label} must be at least ${field.validation.minLength} characters`
      });
    }
    if (field.validation.maxLength && value.toString().length > field.validation.maxLength) {
      errors.push({
        field: field.fieldName,
        message: `${field.label} must not exceed ${field.validation.maxLength} characters`
      });
    }
    if (field.validation.min && Number(value) < field.validation.min) {
      errors.push({
        field: field.fieldName,
        message: `${field.label} must be at least ${field.validation.min}`
      });
    }
    if (field.validation.max && Number(value) > field.validation.max) {
      errors.push({
        field: field.fieldName,
        message: `${field.label} must not exceed ${field.validation.max}`
      });
    }
    if (field.validation.pattern) {
      const regex = new RegExp(field.validation.pattern);
      if (!regex.test(value)) {
        errors.push({
          field: field.fieldName,
          message: `${field.label} format is invalid`
        });
      }
    }
  }
  
  return errors;
};

// Static methods
LoanFormSchemaModel.statics.getActiveSchemaByLoanType = function(loanType) {
  return this.findOne({ loanType, isActive: true });
};

LoanFormSchemaModel.statics.getAllActiveSchemas = function() {
  return this.find({ isActive: true }).sort({ loanTypeName: 1 });
};

const LoanFormSchema = mongoose.model('LoanFormSchema', LoanFormSchemaModel);

module.exports = LoanFormSchema;
