const mongoose = require('mongoose');

// Model to store submitted form data
const LoanFormDataSchema = new mongoose.Schema({
  // Reference to the form schema used
  formSchema: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'LoanFormSchema',
    required: true
  },
  
  // Reference to the loan workflow
  workflowId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'LoanWorkflow',
    required: true
  },
  
  // Loan type for quick filtering
  loanType: {
    type: String,
    required: true,
    enum: ['vehicle_loan', 'housing_loan', 'personal_loan', 'business_loan', 'education_loan', 'gold_loan']
  },
  
  // Raw form data as submitted by frontend
  formData: {
    type: mongoose.Schema.Types.Mixed,
    required: true
  },
  
  // Processed and mapped data for primary application
  mappedData: {
    type: mongoose.Schema.Types.Mixed
  },
  
  // Validation results
  validationResults: {
    isValid: {
      type: Boolean,
      default: false
    },
    errors: [{
      field: String,
      message: String,
      code: String
    }],
    warnings: [{
      field: String,
      message: String,
      code: String
    }]
  },
  
  // PDF generation status
  pdfStatus: {
    isGenerated: {
      type: Boolean,
      default: false
    },
    pdfPath: String,
    pdfUrl: String,
    generatedAt: Date,
    generationError: String
  },
  
  // Document attachments
  attachments: [{
    fieldName: String,
    originalName: String,
    fileName: String,
    filePath: String,
    fileSize: Number,
    mimeType: String,
    uploadedAt: {
      type: Date,
      default: Date.now
    }
  }],
  
  // Form submission metadata
  submissionMetadata: {
    ipAddress: String,
    userAgent: String,
    submissionTime: {
      type: Date,
      default: Date.now
    },
    formVersion: String,
    sessionId: String
  },
  
  // User who submitted the form
  submittedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  
  // Branch information
  branch: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Branch',
    required: true
  },
  
  // Status tracking
  status: {
    type: String,
    enum: ['draft', 'submitted', 'validated', 'pdf_generated', 'completed', 'error'],
    default: 'draft'
  },
  
  // Version control for form data
  version: {
    type: Number,
    default: 1
  },
  
  // History of changes
  changeHistory: [{
    changedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    changedAt: {
      type: Date,
      default: Date.now
    },
    changeType: {
      type: String,
      enum: ['created', 'updated', 'validated', 'pdf_generated', 'submitted']
    },
    changes: mongoose.Schema.Types.Mixed,
    comments: String
  }],
  
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Indexes for performance
LoanFormDataSchema.index({ workflowId: 1 });
LoanFormDataSchema.index({ loanType: 1, status: 1 });
LoanFormDataSchema.index({ submittedBy: 1, createdAt: -1 });
LoanFormDataSchema.index({ branch: 1, status: 1 });

// Virtual for getting form schema details
LoanFormDataSchema.virtual('schemaDetails', {
  ref: 'LoanFormSchema',
  localField: 'formSchema',
  foreignField: '_id',
  justOne: true
});

// Methods
LoanFormDataSchema.methods.addChangeHistory = function(changeType, changedBy, changes, comments) {
  this.changeHistory.push({
    changedBy,
    changeType,
    changes,
    comments
  });
  return this.save();
};

LoanFormDataSchema.methods.updateStatus = function(newStatus, changedBy, comments) {
  const oldStatus = this.status;
  this.status = newStatus;
  
  return this.addChangeHistory('updated', changedBy, {
    status: { from: oldStatus, to: newStatus }
  }, comments);
};

LoanFormDataSchema.methods.validateFormData = async function() {
  try {
    const schema = await mongoose.model('LoanFormSchema').findById(this.formSchema);
    if (!schema) {
      throw new Error('Form schema not found');
    }
    
    const validationResults = schema.validateFormData(this.formData);
    
    this.validationResults = {
      isValid: validationResults.length === 0,
      errors: validationResults,
      warnings: []
    };
    
    if (this.validationResults.isValid) {
      this.status = 'validated';
    } else {
      this.status = 'error';
    }
    
    await this.save();
    return this.validationResults;
    
  } catch (error) {
    this.validationResults = {
      isValid: false,
      errors: [{ field: 'general', message: error.message, code: 'VALIDATION_ERROR' }],
      warnings: []
    };
    this.status = 'error';
    await this.save();
    throw error;
  }
};

LoanFormDataSchema.methods.mapToPrimaryApplication = async function() {
  try {
    const schema = await mongoose.model('LoanFormSchema').findById(this.formSchema);
    if (!schema) {
      throw new Error('Form schema not found');
    }
    
    const mappedData = {};
    
    // Map each field according to its primaryApplicationMapping
    schema.fields.forEach(field => {
      if (field.primaryApplicationMapping && field.primaryApplicationMapping.fieldPath) {
        const value = this.formData[field.fieldName];
        if (value !== undefined && value !== null) {
          // Set nested object properties using dot notation
          this.setNestedProperty(mappedData, field.primaryApplicationMapping.fieldPath, value);
        }
      }
    });
    
    this.mappedData = mappedData;
    await this.save();
    
    return mappedData;
    
  } catch (error) {
    console.error('Error mapping form data:', error);
    throw error;
  }
};

LoanFormDataSchema.methods.setNestedProperty = function(obj, path, value) {
  const keys = path.split('.');
  let current = obj;
  
  for (let i = 0; i < keys.length - 1; i++) {
    const key = keys[i];
    if (!(key in current)) {
      current[key] = {};
    }
    current = current[key];
  }
  
  current[keys[keys.length - 1]] = value;
};

LoanFormDataSchema.methods.generatePDFData = async function() {
  try {
    const schema = await mongoose.model('LoanFormSchema').findById(this.formSchema);
    if (!schema) {
      throw new Error('Form schema not found');
    }
    
    const pdfData = {};
    
    // Map each field according to its pdfMapping
    schema.fields.forEach(field => {
      if (field.pdfMapping && field.pdfMapping.templateVariable) {
        let value = this.formData[field.fieldName];
        
        // Apply formatter if specified
        if (field.pdfMapping.formatter && value !== undefined && value !== null) {
          value = this.applyFormatter(field.pdfMapping.formatter, value);
        }
        
        pdfData[field.pdfMapping.templateVariable] = value;
      }
    });
    
    // Add metadata
    pdfData.generatedAt = new Date().toLocaleString();
    pdfData.applicationId = this.workflowId;
    pdfData.loanType = this.loanType;
    
    return pdfData;
    
  } catch (error) {
    console.error('Error generating PDF data:', error);
    throw error;
  }
};

LoanFormDataSchema.methods.applyFormatter = function(formatterName, value) {
  // Define formatters for different data types
  const formatters = {
    currency: (val) => `₹${Number(val).toLocaleString('en-IN')}`,
    percentage: (val) => `${val}%`,
    date: (val) => new Date(val).toLocaleDateString('en-IN'),
    phone: (val) => val.toString().replace(/(\d{3})(\d{3})(\d{4})/, '$1-$2-$3'),
    uppercase: (val) => val.toString().toUpperCase(),
    lowercase: (val) => val.toString().toLowerCase(),
    capitalize: (val) => val.toString().charAt(0).toUpperCase() + val.toString().slice(1)
  };
  
  return formatters[formatterName] ? formatters[formatterName](value) : value;
};

// Static methods
LoanFormDataSchema.statics.getByWorkflowId = function(workflowId) {
  return this.findOne({ workflowId, isActive: true })
             .populate('formSchema')
             .populate('submittedBy', 'firstName lastName email')
             .populate('branch', 'name code');
};

LoanFormDataSchema.statics.getByUserAndStatus = function(userId, status, limit = 10) {
  const query = { submittedBy: userId, isActive: true };
  if (status) query.status = status;
  
  return this.find(query)
             .populate('formSchema', 'loanTypeName')
             .populate('branch', 'name')
             .sort({ updatedAt: -1 })
             .limit(limit);
};

const LoanFormData = mongoose.model('LoanFormData', LoanFormDataSchema);

module.exports = LoanFormData;
