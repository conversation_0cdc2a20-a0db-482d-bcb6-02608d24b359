const mongoose = require('mongoose');
const LoanFormSchema = require('../models/loanFormSchema.model');
const { autoGenerateFieldMappings } = require('../utils/formMapping');

// Vehicle Loan Form Schema
const vehicleLoanSchema = {
  loanType: 'vehicle_loan',
  loanTypeName: 'Vehicle Loan',
  version: '1.0.0',
  description: 'Comprehensive form for vehicle loan applications',
  
  sections: [
    {
      sectionName: 'personal_info',
      title: 'Personal Information',
      description: 'Basic personal details of the applicant',
      order: 1,
      isCollapsible: false,
      isExpanded: true
    },
    {
      sectionName: 'contact_info',
      title: 'Contact Information',
      description: 'Contact details and addresses',
      order: 2,
      isCollapsible: true,
      isExpanded: true
    },
    {
      sectionName: 'vehicle_details',
      title: 'Vehicle Details',
      description: 'Information about the vehicle to be purchased',
      order: 3,
      isCollapsible: true,
      isExpanded: true
    },
    {
      sectionName: 'financial_info',
      title: 'Financial Information',
      description: 'Income and financial details',
      order: 4,
      isCollapsible: true,
      isExpanded: true
    },
    {
      sectionName: 'loan_details',
      title: 'Loan Details',
      description: 'Loan amount and tenure preferences',
      order: 5,
      isCollapsible: true,
      isExpanded: true
    }
  ],
  
  fields: [
    // Personal Information
    {
      fieldName: 'firstName',
      fieldType: 'text',
      label: 'First Name',
      placeholder: 'Enter your first name',
      isRequired: true,
      section: 'personal_info',
      order: 1,
      validation: { minLength: 2, maxLength: 50 },
      primaryApplicationMapping: { fieldPath: 'personalInfo.fname' },
      pdfMapping: { templateVariable: 'applicant_first_name' }
    },
    {
      fieldName: 'middleName',
      fieldType: 'text',
      label: 'Middle Name',
      placeholder: 'Enter your middle name',
      isRequired: false,
      section: 'personal_info',
      order: 2,
      validation: { maxLength: 50 },
      primaryApplicationMapping: { fieldPath: 'personalInfo.mname' },
      pdfMapping: { templateVariable: 'applicant_middle_name' }
    },
    {
      fieldName: 'lastName',
      fieldType: 'text',
      label: 'Last Name',
      placeholder: 'Enter your last name',
      isRequired: true,
      section: 'personal_info',
      order: 3,
      validation: { minLength: 2, maxLength: 50 },
      primaryApplicationMapping: { fieldPath: 'personalInfo.lname' },
      pdfMapping: { templateVariable: 'applicant_last_name' }
    },
    {
      fieldName: 'dateOfBirth',
      fieldType: 'date',
      label: 'Date of Birth',
      isRequired: true,
      section: 'personal_info',
      order: 4,
      primaryApplicationMapping: { fieldPath: 'personalInfo.dateOfBirth' },
      pdfMapping: { templateVariable: 'applicant_dob', formatter: 'date' }
    },
    {
      fieldName: 'gender',
      fieldType: 'radio',
      label: 'Gender',
      isRequired: true,
      section: 'personal_info',
      order: 5,
      options: [
        { label: 'Male', value: 'male' },
        { label: 'Female', value: 'female' },
        { label: 'Other', value: 'other' }
      ],
      primaryApplicationMapping: { fieldPath: 'personalInfo.gender' },
      pdfMapping: { templateVariable: 'applicant_gender' }
    },
    {
      fieldName: 'maritalStatus',
      fieldType: 'select',
      label: 'Marital Status',
      isRequired: true,
      section: 'personal_info',
      order: 6,
      options: [
        { label: 'Single', value: 'single' },
        { label: 'Married', value: 'married' },
        { label: 'Divorced', value: 'divorced' },
        { label: 'Widowed', value: 'widowed' }
      ],
      primaryApplicationMapping: { fieldPath: 'personalInfo.maritalStatus' },
      pdfMapping: { templateVariable: 'applicant_marital_status' }
    },
    
    // Contact Information
    {
      fieldName: 'email',
      fieldType: 'email',
      label: 'Email Address',
      placeholder: 'Enter your email address',
      isRequired: true,
      section: 'contact_info',
      order: 1,
      primaryApplicationMapping: { fieldPath: 'personalInfo.email' },
      pdfMapping: { templateVariable: 'applicant_email' }
    },
    {
      fieldName: 'phoneNumber',
      fieldType: 'phone',
      label: 'Mobile Number',
      placeholder: 'Enter your 10-digit mobile number',
      isRequired: true,
      section: 'contact_info',
      order: 2,
      validation: { pattern: '^[6-9]\\d{9}$' },
      primaryApplicationMapping: { fieldPath: 'phone.number' },
      pdfMapping: { templateVariable: 'applicant_phone', formatter: 'phone' }
    },
    {
      fieldName: 'homeAddressLine1',
      fieldType: 'text',
      label: 'Address Line 1',
      placeholder: 'House/Flat number, Building name',
      isRequired: true,
      section: 'contact_info',
      order: 3,
      validation: { maxLength: 100 },
      primaryApplicationMapping: { fieldPath: 'homeAddress.line1' },
      pdfMapping: { templateVariable: 'home_address_line1' }
    },
    {
      fieldName: 'homeCity',
      fieldType: 'text',
      label: 'City',
      placeholder: 'Enter your city',
      isRequired: true,
      section: 'contact_info',
      order: 4,
      primaryApplicationMapping: { fieldPath: 'homeAddress.city' },
      pdfMapping: { templateVariable: 'home_city' }
    },
    {
      fieldName: 'homePincode',
      fieldType: 'text',
      label: 'Pincode',
      placeholder: 'Enter 6-digit pincode',
      isRequired: true,
      section: 'contact_info',
      order: 5,
      validation: { pattern: '^\\d{6}$' },
      primaryApplicationMapping: { fieldPath: 'homeAddress.pincode' },
      pdfMapping: { templateVariable: 'home_pincode' }
    },
    
    // Vehicle Details
    {
      fieldName: 'vehicleType',
      fieldType: 'select',
      label: 'Vehicle Type',
      isRequired: true,
      section: 'vehicle_details',
      order: 1,
      options: [
        { label: 'Two Wheeler', value: 'two_wheeler' },
        { label: 'Four Wheeler', value: 'four_wheeler' },
        { label: 'Commercial Vehicle', value: 'commercial' }
      ],
      primaryApplicationMapping: { fieldPath: 'loanSpecific.vehicleType' },
      pdfMapping: { templateVariable: 'vehicle_type' }
    },
    {
      fieldName: 'vehicleMake',
      fieldType: 'text',
      label: 'Vehicle Make',
      placeholder: 'e.g., Maruti, Honda, Tata',
      isRequired: true,
      section: 'vehicle_details',
      order: 2,
      primaryApplicationMapping: { fieldPath: 'loanSpecific.vehicleMake' },
      pdfMapping: { templateVariable: 'vehicle_make' }
    },
    {
      fieldName: 'vehicleModel',
      fieldType: 'text',
      label: 'Vehicle Model',
      placeholder: 'e.g., Swift, City, Nexon',
      isRequired: true,
      section: 'vehicle_details',
      order: 3,
      primaryApplicationMapping: { fieldPath: 'loanSpecific.vehicleModel' },
      pdfMapping: { templateVariable: 'vehicle_model' }
    },
    {
      fieldName: 'vehiclePrice',
      fieldType: 'currency',
      label: 'Vehicle Price',
      placeholder: 'Enter vehicle price in rupees',
      isRequired: true,
      section: 'vehicle_details',
      order: 4,
      validation: { min: 50000, max: 10000000 },
      primaryApplicationMapping: { fieldPath: 'loanSpecific.vehiclePrice' },
      pdfMapping: { templateVariable: 'vehicle_price', formatter: 'currency' }
    },
    {
      fieldName: 'downPayment',
      fieldType: 'currency',
      label: 'Down Payment',
      placeholder: 'Enter down payment amount',
      isRequired: true,
      section: 'vehicle_details',
      order: 5,
      validation: { min: 0 },
      primaryApplicationMapping: { fieldPath: 'loanSpecific.downPayment' },
      pdfMapping: { templateVariable: 'down_payment', formatter: 'currency' }
    },
    
    // Financial Information
    {
      fieldName: 'monthlyIncome',
      fieldType: 'currency',
      label: 'Monthly Income',
      placeholder: 'Enter your monthly income',
      isRequired: true,
      section: 'financial_info',
      order: 1,
      validation: { min: 15000 },
      primaryApplicationMapping: { fieldPath: 'occupation.monthlyIncome' },
      pdfMapping: { templateVariable: 'monthly_income', formatter: 'currency' }
    },
    {
      fieldName: 'companyName',
      fieldType: 'text',
      label: 'Company Name',
      placeholder: 'Enter your company name',
      isRequired: true,
      section: 'financial_info',
      order: 2,
      primaryApplicationMapping: { fieldPath: 'occupation.companyName' },
      pdfMapping: { templateVariable: 'company_name' }
    },
    {
      fieldName: 'workExperience',
      fieldType: 'number',
      label: 'Work Experience (Years)',
      placeholder: 'Enter years of experience',
      isRequired: true,
      section: 'financial_info',
      order: 3,
      validation: { min: 0, max: 50 },
      primaryApplicationMapping: { fieldPath: 'occupation.workExperience' },
      pdfMapping: { templateVariable: 'work_experience' }
    },
    
    // Loan Details
    {
      fieldName: 'loanAmount',
      fieldType: 'currency',
      label: 'Loan Amount Required',
      placeholder: 'Enter loan amount',
      isRequired: true,
      section: 'loan_details',
      order: 1,
      validation: { min: 50000, max: 5000000 },
      primaryApplicationMapping: { fieldPath: 'loanInfo.amount' },
      pdfMapping: { templateVariable: 'loan_amount', formatter: 'currency' }
    },
    {
      fieldName: 'loanTenure',
      fieldType: 'select',
      label: 'Loan Tenure',
      isRequired: true,
      section: 'loan_details',
      order: 2,
      options: [
        { label: '1 Year', value: '12' },
        { label: '2 Years', value: '24' },
        { label: '3 Years', value: '36' },
        { label: '4 Years', value: '48' },
        { label: '5 Years', value: '60' },
        { label: '7 Years', value: '84' }
      ],
      primaryApplicationMapping: { fieldPath: 'loanInfo.tenure' },
      pdfMapping: { templateVariable: 'loan_tenure' }
    }
  ],
  
  pdfTemplate: {
    templateName: 'vehicle_loan_application',
    templatePath: './templates/pdf/vehicle_loan.hbs',
    orientation: 'portrait',
    format: 'A4'
  },
  
  workflowConfig: {
    autoSubmitToBM: false,
    requiredDocuments: ['identity_proof', 'address_proof', 'income_proof', 'vehicle_quotation'],
    approvalThresholds: {
      branchManager: 2500000,
      generalManager: 5000000
    }
  }
};

// Personal Loan Form Schema
const personalLoanSchema = {
  loanType: 'personal_loan',
  loanTypeName: 'Personal Loan',
  version: '1.0.0',
  description: 'Simple form for personal loan applications',
  
  sections: [
    {
      sectionName: 'personal_info',
      title: 'Personal Information',
      order: 1,
      isCollapsible: false,
      isExpanded: true
    },
    {
      sectionName: 'financial_info',
      title: 'Financial Information',
      order: 2,
      isCollapsible: true,
      isExpanded: true
    },
    {
      sectionName: 'loan_details',
      title: 'Loan Requirements',
      order: 3,
      isCollapsible: true,
      isExpanded: true
    }
  ],
  
  fields: [
    // Personal Information
    {
      fieldName: 'firstName',
      fieldType: 'text',
      label: 'First Name',
      isRequired: true,
      section: 'personal_info',
      order: 1,
      primaryApplicationMapping: { fieldPath: 'personalInfo.fname' },
      pdfMapping: { templateVariable: 'applicant_first_name' }
    },
    {
      fieldName: 'lastName',
      fieldType: 'text',
      label: 'Last Name',
      isRequired: true,
      section: 'personal_info',
      order: 2,
      primaryApplicationMapping: { fieldPath: 'personalInfo.lname' },
      pdfMapping: { templateVariable: 'applicant_last_name' }
    },
    {
      fieldName: 'email',
      fieldType: 'email',
      label: 'Email Address',
      isRequired: true,
      section: 'personal_info',
      order: 3,
      primaryApplicationMapping: { fieldPath: 'personalInfo.email' },
      pdfMapping: { templateVariable: 'applicant_email' }
    },
    {
      fieldName: 'phoneNumber',
      fieldType: 'phone',
      label: 'Mobile Number',
      isRequired: true,
      section: 'personal_info',
      order: 4,
      primaryApplicationMapping: { fieldPath: 'phone.number' },
      pdfMapping: { templateVariable: 'applicant_phone', formatter: 'phone' }
    },
    
    // Financial Information
    {
      fieldName: 'monthlyIncome',
      fieldType: 'currency',
      label: 'Monthly Income',
      isRequired: true,
      section: 'financial_info',
      order: 1,
      validation: { min: 20000 },
      primaryApplicationMapping: { fieldPath: 'occupation.monthlyIncome' },
      pdfMapping: { templateVariable: 'monthly_income', formatter: 'currency' }
    },
    {
      fieldName: 'companyName',
      fieldType: 'text',
      label: 'Company Name',
      isRequired: true,
      section: 'financial_info',
      order: 2,
      primaryApplicationMapping: { fieldPath: 'occupation.companyName' },
      pdfMapping: { templateVariable: 'company_name' }
    },
    
    // Loan Details
    {
      fieldName: 'loanAmount',
      fieldType: 'currency',
      label: 'Loan Amount',
      isRequired: true,
      section: 'loan_details',
      order: 1,
      validation: { min: 50000, max: 2000000 },
      primaryApplicationMapping: { fieldPath: 'loanInfo.amount' },
      pdfMapping: { templateVariable: 'loan_amount', formatter: 'currency' }
    },
    {
      fieldName: 'loanPurpose',
      fieldType: 'select',
      label: 'Loan Purpose',
      isRequired: true,
      section: 'loan_details',
      order: 2,
      options: [
        { label: 'Medical Emergency', value: 'medical' },
        { label: 'Wedding', value: 'wedding' },
        { label: 'Travel', value: 'travel' },
        { label: 'Home Renovation', value: 'renovation' },
        { label: 'Education', value: 'education' },
        { label: 'Other', value: 'other' }
      ],
      primaryApplicationMapping: { fieldPath: 'loanInfo.purpose' },
      pdfMapping: { templateVariable: 'loan_purpose' }
    }
  ],
  
  pdfTemplate: {
    templateName: 'personal_loan_application',
    templatePath: './templates/pdf/personal_loan.hbs',
    orientation: 'portrait',
    format: 'A4'
  },
  
  workflowConfig: {
    autoSubmitToBM: false,
    requiredDocuments: ['identity_proof', 'address_proof', 'income_proof'],
    approvalThresholds: {
      branchManager: 1000000,
      generalManager: 2000000
    }
  }
};

// Function to seed form schemas
const seedFormSchemas = async () => {
  try {
    console.log('Seeding loan form schemas...');
    
    // Clear existing schemas
    await LoanFormSchema.deleteMany({});
    
    // Create schemas with auto-generated mappings
    const schemas = [vehicleLoanSchema, personalLoanSchema];
    
    for (const schemaData of schemas) {
      // Auto-generate any missing field mappings
      schemaData.fields = autoGenerateFieldMappings(schemaData.fields, schemaData.loanType);
      
      const schema = new LoanFormSchema({
        ...schemaData,
        createdBy: new mongoose.Types.ObjectId() // Placeholder user ID
      });
      
      await schema.save();
      console.log(`✅ Created ${schemaData.loanTypeName} schema`);
    }
    
    console.log('✅ Form schemas seeded successfully');
    
  } catch (error) {
    console.error('❌ Error seeding form schemas:', error);
    throw error;
  }
};

module.exports = {
  seedFormSchemas,
  vehicleLoanSchema,
  personalLoanSchema
};
