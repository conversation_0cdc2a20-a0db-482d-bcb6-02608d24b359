const { LoanWorkflow, WOR<PERSON>FLOW_STATUS, WORKFLOW_ACTIONS, LOAN_AMOUNT_THRESHOLD } = require('../models/loanWorkflow.model');
const User = require('../models/user.model');

/**
 * Middleware to validate workflow actions based on loan amount and user role
 */
const validateWorkflowAction = async (req, res, next) => {
  try {
    const { workflowId } = req.params;
    const { action } = req.body;
    const userRole = req.user.role;
    const userId = req.user.userId;

    // Get workflow
    const workflow = await LoanWorkflow.findById(workflowId);
    if (!workflow) {
      return res.status(404).json({
        success: false,
        message: 'Workflow not found'
      });
    }

    // Check if user can perform the action
    const actionCheck = workflow.canPerformAction(action, userRole, userId);
    if (!actionCheck.allowed) {
      return res.status(403).json({
        success: false,
        message: actionCheck.reason
      });
    }

    // Special validation for branch manager approval
    if (action === WORKFLOW_ACTIONS.APPROVE_BY_BM && userRole === 'branch_manager') {
      if (workflow.shouldForwardToGM()) {
        return res.status(400).json({
          success: false,
          message: `Cannot approve directly. Loan amount ₹${workflow.loanAmount.toLocaleString()} exceeds the ₹${LOAN_AMOUNT_THRESHOLD.toLocaleString()} threshold. Please forward to General Manager.`,
          suggestedAction: WORKFLOW_ACTIONS.FORWARD_TO_GM,
          loanAmount: workflow.loanAmount,
          threshold: LOAN_AMOUNT_THRESHOLD
        });
      }
    }

    // Attach workflow to request for use in controller
    req.workflow = workflow;
    next();

  } catch (error) {
    console.error('Error in workflow validation middleware:', error);
    res.status(500).json({
      success: false,
      message: 'Workflow validation failed',
      error: error.message
    });
  }
};

/**
 * Middleware to check if user is authorized to view/modify specific workflow
 */
const checkWorkflowAccess = async (req, res, next) => {
  try {
    const { workflowId } = req.params;
    const userRole = req.user.role;
    const userId = req.user.userId;
    const userBranch = req.user.branchId;

    const workflow = await LoanWorkflow.findById(workflowId).populate('branch');
    if (!workflow) {
      return res.status(404).json({
        success: false,
        message: 'Workflow not found'
      });
    }

    let hasAccess = false;

    switch (userRole) {
      case 'super_admin':
        hasAccess = true;
        break;
      
      case 'deputy_manager':
        // Deputy manager can only access workflows they created
        hasAccess = workflow.createdBy.toString() === userId.toString();
        break;
      
      case 'branch_manager':
        // Branch manager can access workflows from their branch
        hasAccess = workflow.branch._id.toString() === userBranch?.toString() ||
                   workflow.currentReviewer?.toString() === userId.toString();
        break;
      
      case 'general_manager':
        // General manager can access all workflows, especially those forwarded to them
        hasAccess = true;
        break;
      
      default:
        hasAccess = false;
    }

    if (!hasAccess) {
      return res.status(403).json({
        success: false,
        message: 'You do not have access to this workflow'
      });
    }

    req.workflow = workflow;
    next();

  } catch (error) {
    console.error('Error in workflow access check:', error);
    res.status(500).json({
      success: false,
      message: 'Access check failed',
      error: error.message
    });
  }
};

/**
 * Middleware to validate loan amount and suggest routing
 */
const validateLoanAmount = (req, res, next) => {
  try {
    const { loanAmount } = req.body;

    if (!loanAmount || loanAmount <= 0) {
      return res.status(400).json({
        success: false,
        message: 'Valid loan amount is required'
      });
    }

    // Add routing suggestion to request
    req.routingSuggestion = {
      loanAmount,
      threshold: LOAN_AMOUNT_THRESHOLD,
      requiresGMApproval: loanAmount > LOAN_AMOUNT_THRESHOLD,
      canBMApprove: loanAmount <= LOAN_AMOUNT_THRESHOLD
    };

    next();

  } catch (error) {
    console.error('Error in loan amount validation:', error);
    res.status(500).json({
      success: false,
      message: 'Loan amount validation failed',
      error: error.message
    });
  }
};

/**
 * Middleware to ensure proper workflow sequence
 */
const validateWorkflowSequence = async (req, res, next) => {
  try {
    const { workflowId } = req.params;
    const { action } = req.body;

    const workflow = await LoanWorkflow.findById(workflowId);
    if (!workflow) {
      return res.status(404).json({
        success: false,
        message: 'Workflow not found'
      });
    }

    // Check if the action follows proper sequence
    const currentStatus = workflow.currentStatus;
    const expectedNextStatus = workflow.getNextStatus(action);

    if (!expectedNextStatus || expectedNextStatus === currentStatus) {
      return res.status(400).json({
        success: false,
        message: 'Invalid workflow transition',
        currentStatus,
        attemptedAction: action
      });
    }

    // Validate specific sequences
    switch (action) {
      case WORKFLOW_ACTIONS.SUBMIT_TO_BM:
        if (currentStatus !== WORKFLOW_STATUS.DRAFT && 
            currentStatus !== WORKFLOW_STATUS.RETURNED_FOR_CHANGES) {
          return res.status(400).json({
            success: false,
            message: 'Can only submit applications that are in draft or returned status'
          });
        }
        break;

      case WORKFLOW_ACTIONS.APPROVE_BY_BM:
      case WORKFLOW_ACTIONS.FORWARD_TO_GM:
      case WORKFLOW_ACTIONS.REJECT:
      case WORKFLOW_ACTIONS.RETURN_FOR_CHANGES:
        if (currentStatus !== WORKFLOW_STATUS.SUBMITTED_TO_BM) {
          return res.status(400).json({
            success: false,
            message: 'Application must be submitted to branch manager first'
          });
        }
        break;

      case WORKFLOW_ACTIONS.APPROVE_BY_GM:
        if (currentStatus !== WORKFLOW_STATUS.FORWARDED_TO_GM) {
          return res.status(400).json({
            success: false,
            message: 'Application must be forwarded to general manager first'
          });
        }
        break;
    }

    req.workflow = workflow;
    next();

  } catch (error) {
    console.error('Error in workflow sequence validation:', error);
    res.status(500).json({
      success: false,
      message: 'Workflow sequence validation failed',
      error: error.message
    });
  }
};

/**
 * Middleware to log workflow actions for audit trail
 */
const logWorkflowAction = (req, res, next) => {
  const originalSend = res.send;
  
  res.send = function(data) {
    // Log the action if it was successful
    if (res.statusCode >= 200 && res.statusCode < 300) {
      console.log('Workflow Action Log:', {
        workflowId: req.params.workflowId,
        action: req.body.action,
        performedBy: req.user.userId,
        performedByRole: req.user.role,
        timestamp: new Date().toISOString(),
        loanAmount: req.workflow?.loanAmount,
        fromStatus: req.workflow?.currentStatus
      });
    }
    
    originalSend.call(this, data);
  };
  
  next();
};

module.exports = {
  validateWorkflowAction,
  checkWorkflowAccess,
  validateLoanAmount,
  validateWorkflowSequence,
  logWorkflowAction
};
