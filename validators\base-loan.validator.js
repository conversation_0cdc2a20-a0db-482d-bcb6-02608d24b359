const Joi = require('joi');
const { validate } = require('../middleware/validation.middleware');
const { objectId } = require('./index');
const {
  personalDetailsSchema,
  employmentDetailsSchema,
  documentSchema,
  addressSchema
} = require('./schemas/common.schemas');

// Base loan schema that all loan types will extend
const baseLoanSchema = Joi.object({
  // Application Info
  applicationInfo: Joi.object({
    applicationNumber: Joi.string().required()
      .messages({ 'any.required': 'Application number is required' }),
    applicationDate: Joi.date().required()
      .messages({ 'any.required': 'Application date is required' }),
    loanType: Joi.string().required()
      .messages({ 'any.required': 'Loan type is required' }),
    branch: objectId.required()
      .messages({ 'any.required': 'Branch is required' }),
    riskCategory: Joi.string().valid('high', 'medium', 'low')
      .messages({ 'any.only': 'Invalid risk category' })
  }).required(),

  // Primary Applicant
  primaryApplicant: Joi.object({
    personalDetails: personalDetailsSchema.required(),
    employmentDetails: employmentDetailsSchema.required(),
    existingLoans: Joi.array().items(
      Joi.object({
        bank: Joi.string().required()
          .messages({ 'any.required': 'Bank name is required' }),
        loanType: Joi.string().required()
          .messages({ 'any.required': 'Loan type is required' }),
        limit: Joi.number().min(0).required()
          .messages({ 'any.required': 'Loan limit is required' }),
        outstanding: Joi.number().min(0).required()
          .messages({ 'any.required': 'Outstanding amount is required' }),
        emi: Joi.number().min(0).required()
          .messages({ 'any.required': 'EMI amount is required' }),
        collateral: Joi.string().allow('')
      })
    ).default([])
  }).required(),

  // Co-Applicants
  coApplicants: Joi.array().items(
    Joi.object({
      relationship: Joi.string().required()
        .messages({ 'any.required': 'Relationship is required' }),
      personalDetails: personalDetailsSchema.required(),
      employmentDetails: employmentDetailsSchema.required()
    })
  ).default([]),

  // Documents
  documents: Joi.array().items(documentSchema).min(1)
    .messages({
      'array.min': 'At least one document is required',
      'any.required': 'Documents are required'
    })
    .required(),

  // Status and Metadata
  status: Joi.string()
    .valid('draft', 'submitted', 'in_review', 'approved', 'rejected', 'disbursed')
    .default('draft'),
  createdBy: objectId.required(),
  updatedBy: objectId.required(),
  createdAt: Joi.date().default(() => new Date(), 'Current date'),
  updatedAt: Joi.date().default(() => new Date(), 'Current date')
});

// Base validation middleware
const validateBaseLoan = (req, res, next) => {
  return validate(baseLoanSchema, 'body')(req, res, next);
};

// Base update validation middleware
const validateBaseLoanUpdate = (req, res, next) => {
  const updateSchema = baseLoanSchema.fork(
    ['applicationInfo', 'primaryApplicant', 'coApplicants', 'documents'],
    (schema) => schema.optional()
  );
  
  return validate(updateSchema, 'body')(req, res, next);
};

module.exports = {
  baseLoanSchema,
  validateBaseLoan,
  validateBaseLoanUpdate
};
