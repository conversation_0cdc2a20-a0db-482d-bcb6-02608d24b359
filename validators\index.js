const Joi = require('joi');
const { validate } = require('../middleware/validation.middleware');

// Common validation patterns with custom error messages
const patterns = {
    email: Joi.string()
        .email({ tlds: { allow: false } })
        .trim()
        .lowercase()
        .required()
        .messages({
            'string.email': 'Please provide a valid email address',
            'string.empty': 'Email is required',
            'any.required': 'Email is required'
        }),
        
    password: Joi.string()
        .min(8)
        .pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/)
        .required()
        .messages({
            'string.min': 'Password must be at least 8 characters long',
            'string.pattern.base': 'Password must contain at least one uppercase letter, one lowercase letter, one number and one special character',
            'string.empty': 'Password is required',
            'any.required': 'Password is required'
        }),
        
    phone: Joi.string()
        .pattern(/^[0-9]{10,15}$/)
        .messages({
            'string.pattern.base': 'Phone number must be 10-15 digits',
            'string.empty': 'Phone number is required'
        }),
        
    objectId: Joi.string()
        .pattern(/^[0-9a-fA-F]{24}$/)
        .message('Invalid ID format')
        .required(),
        
    name: Joi.string()
        .trim()
        .min(2)
        .max(50)
        .pattern(/^[\p{L} .'-]+$/u)
        .messages({
            'string.min': 'Name must be at least 2 characters long',
            'string.max': 'Name cannot be longer than 50 characters',
            'string.pattern.base': 'Name contains invalid characters',
            'string.empty': 'Name is required'
        }),
        
    gender: Joi.string()
        .valid('male', 'female', 'other', 'prefer_not_to_say')
        .messages({
            'any.only': 'Invalid gender value',
            'string.empty': 'Gender is required'
        }),
        
    date: Joi.date()
        .iso()
        .max('now')
        .messages({
            'date.format': 'Date must be in ISO format (YYYY-MM-DD)',
            'date.max': 'Date cannot be in the future'
        }),
        
    role: Joi.string()
        .valid('super_admin', 'employee', 'manager', 'branchmanager', 'managingdirector')
        .messages({
            'any.only': 'Invalid role specified',
            'string.empty': 'Role is required'
        })
};

// User validation schemas
const userSchemas = {
    // Register new user (admin only)
    register: Joi.object({
        email: patterns.email,
        password: patterns.password,
        role: patterns.role.required(),
        firstName: patterns.name.required(),
        middleName: patterns.name.allow(''),
        lastName: patterns.name.required(),
        phone: patterns.phone.required(),
        gender: patterns.gender.required(),
        dateOfBirth: patterns.date.required(),
        branch: patterns.objectId.when('role', {
            is: Joi.valid('superadmin', 'managingdirector'),
            then: Joi.optional(),
            otherwise: Joi.required()
        }),
        department: Joi.string().trim().required(),
        designation: Joi.string().trim().required(),
        dateOfJoining: patterns.date.required(),
        address: Joi.object({
            street: Joi.string().trim().required(),
            city: Joi.string().trim().required(),
            state: Joi.string().trim().required(),
            postalCode: Joi.string().trim().required(),
            country: Joi.string().trim().default('India')
        }).required()
    }),

    // Update profile
    updateProfile: Joi.object({
        firstName: patterns.name,
        middleName: patterns.name.allow(''),
        lastName: patterns.name,
        phone: patterns.phone,
        gender: patterns.gender,
        dateOfBirth: patterns.date,
        address: Joi.object({
            street: Joi.string().trim(),
            city: Joi.string().trim(),
            state: Joi.string().trim(),
            postalCode: Joi.string().trim(),
            country: Joi.string().trim()
        })
    }).min(1), // At least one field is required

    // Change password
    changePassword: Joi.object({
        currentPassword: patterns.password,
        newPassword: patterns.password.invalid(Joi.ref('currentPassword')).messages({
            'any.invalid': 'New password must be different from current password'
        }),
        confirmPassword: Joi.string().valid(Joi.ref('newPassword')).messages({
            'any.only': 'Passwords do not match'
        })
    }),

    // Admin update user
    adminUpdateUser: Joi.object({
        email: patterns.email,
        role: patterns.role,
        isActive: Joi.boolean(),
        branch: patterns.objectId,
        department: Joi.string().trim(),
        designation: Joi.string().trim(),
        dateOfJoining: patterns.date
    }).min(1)
};

// Application validation schemas
const applicationSchemas = {
    create: Joi.object({
        // Add your application creation validation schema here
    }),
    update: Joi.object({
        // Add your application update validation schema here
    }),
    submit: Joi.object({
        // Add your application submission validation schema here
    })
};

// Workflow validation schemas
const workflowSchemas = {
    reviewAction: Joi.object({
        action: Joi.string().valid('approve', 'reject', 'request_changes').required(),
        comments: Joi.string().trim().when('action', {
            is: 'approve',
            then: Joi.string().trim().allow(''),
            otherwise: Joi.string().trim().required()
        })
    })
};

// Export validation middleware functions
module.exports = {
    // User validations
    validateRegister: validate(userSchemas.register),
    validateUpdateProfile: validate(userSchemas.updateProfile),
    validateChangePassword: validate(userSchemas.changePassword),
    validateAdminUpdateUser: validate(userSchemas.adminUpdateUser),
    
    // Application validations
    validateCreateApplication: validate(applicationSchemas.create),
    validateUpdateApplication: validate(applicationSchemas.update),
    validateSubmitApplication: validate(applicationSchemas.submit),
    
    // Workflow validations
    validateReviewAction: validate(workflowSchemas.reviewAction, 'body'),
    
    // Common validators
    validateObjectId: (paramName) => (req, res, next) => {
        const schema = Joi.object({
            [paramName]: patterns.objectId.required()
        });
        return validate(schema, 'params')(req, res, next);
    }
};
