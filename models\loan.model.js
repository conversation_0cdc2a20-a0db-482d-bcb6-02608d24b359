const mongoose = require('mongoose');
const Schema = mongoose.Schema;

// Sub-schemas
const AddressSchema = new Schema({
  line1: { type: String, required: true },
  line2: { type: String, default: '' },
  city: { type: String, required: true },
  state: { type: String, required: true },
  pincode: { type: String, required: true },
  country: { type: String, default: 'India' }
});

const PersonalDetailsSchema = new Schema({
  firstName: { type: String, required: true },
  middleName: { type: String, default: '' },
  lastName: { type: String, required: true },
  gender: { 
    type: String, 
    enum: ['male', 'female', 'other'],
    required: true 
  },
  dateOfBirth: { type: Date, required: true },
  maritalStatus: { 
    type: String, 
    enum: ['single', 'married', 'divorced', 'widowed'],
    required: true 
  },
  pan: { type: String, required: true },
  aadhaar: { type: String, required: true },
  phone: { type: String, required: true },
  email: { type: String, required: true },
  address: { type: AddressSchema, required: true }
});

const EmploymentDetailsSchema = new Schema({
  employmentType: {
    type: String,
    enum: ['salaried', 'self_employed', 'professional', 'other'],
    required: true
  },
  companyName: { type: String },
  designation: { type: String },
  monthlyIncome: { type: Number, required: true },
  businessName: { type: String },
  businessType: { type: String },
  businessAddress: { type: AddressSchema }
});

const ExistingLoanSchema = new Schema({
  bank: { type: String, required: true },
  loanType: { type: String, required: true },
  limit: { type: Number, required: true },
  outstanding: { type: Number, required: true },
  emi: { type: Number, required: true },
  collateral: { type: String, default: '' }
});

const DocumentSchema = new Schema({
  type: { type: String, required: true },
  url: { type: String, required: true },
  name: { type: String, required: true },
  uploadedAt: { type: Date, default: Date.now }
});

// Main Loan Schema
const LoanSchema = new Schema({
  // Application Info
  applicationInfo: {
    applicationNumber: { type: String, required: true, unique: true },
    applicationDate: { type: Date, default: Date.now },
    loanType: { 
      type: String, 
      required: true,
      enum: ['housing', 'vehicle', 'personal'] // Add more as needed
    },
    branch: { 
      type: Schema.Types.ObjectId, 
      ref: 'Branch',
      required: true 
    },
    riskCategory: {
      type: String,
      enum: ['high', 'medium', 'low'],
      default: 'medium'
    }
  },

  // Primary Applicant
  primaryApplicant: {
    personalDetails: { type: PersonalDetailsSchema, required: true },
    employmentDetails: { type: EmploymentDetailsSchema, required: true },
    existingLoans: [ExistingLoanSchema]
  },

  // Co-Applicants
  coApplicants: [{
    relationship: { type: String, required: true },
    personalDetails: { type: PersonalDetailsSchema, required: true },
    employmentDetails: { type: EmploymentDetailsSchema, required: true }
  }],

  // Documents
  documents: [DocumentSchema],

  // Status and Metadata
  status: {
    type: String,
    enum: ['draft', 'submitted', 'in_review', 'approved', 'rejected', 'disbursed'],
    default: 'draft'
  },
  createdBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'User',
    required: true 
  },
  updatedBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'User',
    required: true 
  },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better query performance
LoanSchema.index({ 'applicationInfo.applicationNumber': 1 });
LoanSchema.index({ 'primaryApplicant.personalDetails.email': 1 });
LoanSchema.index({ 'primaryApplicant.personalDetails.pan': 1 });
LoanSchema.index({ 'primaryApplicant.personalDetails.aadhaar': 1 });
LoanSchema.index({ 'applicationInfo.loanType': 1, status: 1 });

// Pre-save hook to generate application number
LoanSchema.pre('save', async function(next) {
  if (this.isNew) {
    const count = await this.constructor.countDocuments();
    this.applicationInfo.applicationNumber = `APP-${Date.now()}-${count + 1}`;
  }
  next();
});

// Virtual for loan status history
LoanSchema.virtual('statusHistory', {
  ref: 'LoanStatusHistory',
  localField: '_id',
  foreignField: 'loanId'
});

const Loan = mongoose.model('Loan', LoanSchema);

module.exports = Loan;
