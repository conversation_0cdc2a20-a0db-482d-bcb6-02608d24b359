const mongoose = require('mongoose');
const LoanFormSchema = require('../models/loanFormSchema.model');

// Updated Vehicle Loan Schema matching your frontend structure
const updatedVehicleLoanSchema = {
  loanType: 'vehicle_loan',
  loanTypeName: 'Vehicle Loan Application',
  loanCode: 'VL',
  version: '2.0.0',
  description: 'Apply for a vehicle loan to purchase your dream car or commercial vehicle',
  subtitle: 'Complete all sections accurately to ensure quick processing of your application',
  category: 'Vehicle Finance',
  estimatedTime: '15-20 minutes',
  
  sections: [
    {
      sectionName: 'applicant_details',
      title: 'Applicant Personal Information',
      description: 'Personal details of the loan applicant',
      iconName: 'User',
      iconColor: 'text-blue-600',
      order: 1,
      isCollapsible: false,
      isExpanded: true,
      gridClass: 'grid-cols-1 md:grid-cols-2'
    },
    {
      sectionName: 'vehicle_details',
      title: 'Vehicle Information',
      description: 'Details about the vehicle you want to purchase',
      iconName: 'Car',
      iconColor: 'text-orange-600',
      order: 2,
      isCollapsible: true,
      isExpanded: true,
      gridClass: 'grid-cols-1 md:grid-cols-2'
    },
    {
      sectionName: 'loan_details',
      title: 'Loan <PERSON>quirements',
      description: 'Specify the loan amount and repayment terms',
      iconName: 'DollarSign',
      iconColor: 'text-green-600',
      order: 3,
      isCollapsible: true,
      isExpanded: true,
      gridClass: 'grid-cols-1 md:grid-cols-2'
    },
    {
      sectionName: 'income_details',
      title: 'Income Information',
      description: 'Details about your income and employment',
      iconName: 'Briefcase',
      iconColor: 'text-purple-600',
      order: 4,
      isCollapsible: true,
      isExpanded: true,
      gridClass: 'grid-cols-1 md:grid-cols-2'
    },
    {
      sectionName: 'financial_details',
      title: 'Financial Information',
      description: 'Additional financial details and existing obligations',
      iconName: 'Calculator',
      iconColor: 'text-indigo-600',
      order: 5,
      isCollapsible: true,
      isExpanded: true,
      gridClass: 'grid-cols-1 md:grid-cols-2'
    },
    {
      sectionName: 'declaration',
      title: 'Declaration',
      description: 'Legal declarations and commitments',
      iconName: 'CheckCircle',
      iconColor: 'text-green-600',
      order: 6,
      isCollapsible: false,
      isExpanded: true,
      gridClass: 'grid-cols-1'
    }
  ],
  
  fields: [
    // Applicant Details Section
    {
      fieldName: 'applicantName',
      fieldType: 'text',
      label: 'Full Name',
      placeholder: 'Enter your complete name',
      isRequired: true,
      section: 'applicant_details',
      order: 1,
      validation: { minLength: 2, maxLength: 100 },
      helpText: 'Name as per official documents',
      primaryApplicationMapping: { fieldPath: 'personalInfo.fname' },
      pdfMapping: { templateVariable: 'applicant_name' }
    },
    {
      fieldName: 'fatherName',
      fieldType: 'text',
      label: "Father's Name",
      placeholder: "Enter father's name",
      isRequired: true,
      section: 'applicant_details',
      order: 2,
      validation: { minLength: 2, maxLength: 100 },
      primaryApplicationMapping: { fieldPath: 'personalInfo.fatherName' },
      pdfMapping: { templateVariable: 'father_name' }
    },
    {
      fieldName: 'dateOfBirth',
      fieldType: 'date',
      label: 'Date of Birth',
      isRequired: true,
      section: 'applicant_details',
      order: 3,
      helpText: 'Date of birth as per official documents',
      primaryApplicationMapping: { fieldPath: 'personalInfo.dateOfBirth' },
      pdfMapping: { templateVariable: 'date_of_birth', formatter: 'date' }
    },
    {
      fieldName: 'age',
      fieldType: 'number',
      label: 'Age',
      placeholder: '25',
      isRequired: true,
      section: 'applicant_details',
      order: 4,
      validation: { min: 18, max: 70 },
      primaryApplicationMapping: { fieldPath: 'personalInfo.age' },
      pdfMapping: { templateVariable: 'applicant_age' }
    },
    {
      fieldName: 'gender',
      fieldType: 'radio',
      label: 'Gender',
      isRequired: true,
      section: 'applicant_details',
      order: 5,
      options: [
        { label: 'Male', value: 'male' },
        { label: 'Female', value: 'female' },
        { label: 'Other', value: 'other' }
      ],
      primaryApplicationMapping: { fieldPath: 'personalInfo.gender' },
      pdfMapping: { templateVariable: 'applicant_gender', formatter: 'capitalize' }
    },
    {
      fieldName: 'maritalStatus',
      fieldType: 'select',
      label: 'Marital Status',
      isRequired: true,
      section: 'applicant_details',
      order: 6,
      options: [
        { label: 'Single', value: 'single' },
        { label: 'Married', value: 'married' },
        { label: 'Divorced', value: 'divorced' },
        { label: 'Widowed', value: 'widowed' }
      ],
      primaryApplicationMapping: { fieldPath: 'personalInfo.maritalStatus' },
      pdfMapping: { templateVariable: 'marital_status', formatter: 'capitalize' }
    },
    {
      fieldName: 'mobileNumber',
      fieldType: 'phone',
      label: 'Mobile Number',
      placeholder: '9876543210',
      isRequired: true,
      section: 'applicant_details',
      order: 7,
      validation: { pattern: '^[6-9]\\d{9}$' },
      primaryApplicationMapping: { fieldPath: 'phone.number' },
      pdfMapping: { templateVariable: 'mobile_number', formatter: 'phone' }
    },
    {
      fieldName: 'email',
      fieldType: 'email',
      label: 'Email Address',
      placeholder: '<EMAIL>',
      isRequired: true,
      section: 'applicant_details',
      order: 8,
      primaryApplicationMapping: { fieldPath: 'personalInfo.email' },
      pdfMapping: { templateVariable: 'email_address' }
    },
    {
      fieldName: 'address',
      fieldType: 'textarea',
      label: 'Permanent Address',
      placeholder: 'Enter complete permanent address',
      isRequired: true,
      section: 'applicant_details',
      order: 9,
      validation: { minLength: 10, maxLength: 500 },
      primaryApplicationMapping: { fieldPath: 'homeAddress.fullAddress' },
      pdfMapping: { templateVariable: 'permanent_address' }
    },

    // Vehicle Details Section
    {
      fieldName: 'vehicleType',
      fieldType: 'select',
      label: 'Vehicle Type',
      isRequired: true,
      section: 'vehicle_details',
      order: 1,
      options: [
        { label: 'Car', value: 'car' },
        { label: 'Two Wheeler', value: 'bike' },
        { label: 'Commercial Vehicle', value: 'commercial' },
        { label: 'Tractor', value: 'tractor' }
      ],
      primaryApplicationMapping: { fieldPath: 'loanSpecific.vehicleType' },
      pdfMapping: { templateVariable: 'vehicle_type', formatter: 'capitalize' }
    },
    {
      fieldName: 'vehicleMake',
      fieldType: 'text',
      label: 'Vehicle Make/Brand',
      placeholder: 'e.g., Maruti, Honda, Tata',
      isRequired: true,
      section: 'vehicle_details',
      order: 2,
      primaryApplicationMapping: { fieldPath: 'loanSpecific.vehicleMake' },
      pdfMapping: { templateVariable: 'vehicle_make' }
    },
    {
      fieldName: 'vehicleModel',
      fieldType: 'text',
      label: 'Vehicle Model',
      placeholder: 'e.g., Swift, City, Nexon',
      isRequired: true,
      section: 'vehicle_details',
      order: 3,
      primaryApplicationMapping: { fieldPath: 'loanSpecific.vehicleModel' },
      pdfMapping: { templateVariable: 'vehicle_model' }
    },
    {
      fieldName: 'vehicleVariant',
      fieldType: 'text',
      label: 'Vehicle Variant',
      placeholder: 'e.g., VXI, ZX, XZ+',
      isRequired: false,
      section: 'vehicle_details',
      order: 4,
      primaryApplicationMapping: { fieldPath: 'loanSpecific.vehicleVariant' },
      pdfMapping: { templateVariable: 'vehicle_variant' }
    },
    {
      fieldName: 'manufacturingYear',
      fieldType: 'number',
      label: 'Manufacturing Year',
      placeholder: '2024',
      isRequired: true,
      section: 'vehicle_details',
      order: 5,
      validation: { min: 2015, max: 2025 },
      primaryApplicationMapping: { fieldPath: 'loanSpecific.manufacturingYear' },
      pdfMapping: { templateVariable: 'manufacturing_year' }
    },
    {
      fieldName: 'vehicleCondition',
      fieldType: 'radio',
      label: 'Vehicle Condition',
      isRequired: true,
      section: 'vehicle_details',
      order: 6,
      options: [
        { label: 'New', value: 'new' },
        { label: 'Used', value: 'used' }
      ],
      primaryApplicationMapping: { fieldPath: 'loanSpecific.vehicleCondition' },
      pdfMapping: { templateVariable: 'vehicle_condition', formatter: 'capitalize' }
    },
    {
      fieldName: 'vehiclePrice',
      fieldType: 'currency',
      label: 'Vehicle Price (₹)',
      placeholder: '500000',
      isRequired: true,
      section: 'vehicle_details',
      order: 7,
      validation: { min: 50000 },
      helpText: 'Ex-showroom price or agreed purchase price',
      primaryApplicationMapping: { fieldPath: 'loanSpecific.vehiclePrice' },
      pdfMapping: { templateVariable: 'vehicle_price', formatter: 'currency' }
    },
    {
      fieldName: 'dealerName',
      fieldType: 'text',
      label: 'Dealer/Seller Name',
      placeholder: 'Enter dealer or seller name',
      isRequired: true,
      section: 'vehicle_details',
      order: 8,
      primaryApplicationMapping: { fieldPath: 'loanSpecific.dealerName' },
      pdfMapping: { templateVariable: 'dealer_name' }
    },

    // Loan Details Section
    {
      fieldName: 'loanAmount',
      fieldType: 'currency',
      label: 'Loan Amount Required (₹)',
      placeholder: '400000',
      isRequired: true,
      section: 'loan_details',
      order: 1,
      validation: { min: 50000, max: 5000000 },
      helpText: 'Amount you want to borrow',
      primaryApplicationMapping: { fieldPath: 'loanInfo.amount' },
      pdfMapping: { templateVariable: 'loan_amount', formatter: 'currency' }
    },
    {
      fieldName: 'downPayment',
      fieldType: 'currency',
      label: 'Down Payment (₹)',
      placeholder: '100000',
      isRequired: true,
      section: 'loan_details',
      order: 2,
      helpText: 'Amount you will pay upfront',
      primaryApplicationMapping: { fieldPath: 'loanSpecific.downPayment' },
      pdfMapping: { templateVariable: 'down_payment', formatter: 'currency' }
    },
    {
      fieldName: 'loanTenure',
      fieldType: 'select',
      label: 'Loan Tenure (Years)',
      isRequired: true,
      section: 'loan_details',
      order: 3,
      options: [
        { label: '1 Year', value: '1' },
        { label: '2 Years', value: '2' },
        { label: '3 Years', value: '3' },
        { label: '4 Years', value: '4' },
        { label: '5 Years', value: '5' },
        { label: '6 Years', value: '6' },
        { label: '7 Years', value: '7' }
      ],
      primaryApplicationMapping: { fieldPath: 'loanInfo.tenure' },
      pdfMapping: { templateVariable: 'loan_tenure' }
    },
    {
      fieldName: 'interestType',
      fieldType: 'radio',
      label: 'Interest Type',
      isRequired: true,
      section: 'loan_details',
      order: 4,
      options: [
        { label: 'Fixed Rate', value: 'fixed' },
        { label: 'Floating Rate', value: 'floating' }
      ],
      primaryApplicationMapping: { fieldPath: 'loanInfo.interestType' },
      pdfMapping: { templateVariable: 'interest_type', formatter: 'capitalize' }
    }
  ],
  
  pdfTemplate: {
    templateName: 'vehicle_loan_application_v2',
    templatePath: './templates/pdf/vehicle_loan_v2.hbs',
    orientation: 'portrait',
    format: 'A4'
  },
  
  workflowConfig: {
    autoSubmitToBM: false,
    requiredDocuments: ['identity_proof', 'address_proof', 'income_proof', 'vehicle_quotation'],
    approvalThresholds: {
      branchManager: 2500000,
      generalManager: 5000000
    }
  }
};

module.exports = { updatedVehicleLoanSchema };
