const express = require('express');
const router = express.Router();
const { authMiddleware, roleMiddleware } = require('../middleware/auth.middleware');
const {
  createApplicationFromFrontend,
  updateApplicationFromFrontend,
  generatePDFFromFrontend
} = require('../controllers/frontendIntegratedApplication.controller');

/**
 * @swagger
 * /frontend-applications:
 *   post:
 *     summary: Create loan application using frontend schema format
 *     tags: [Frontend Applications]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - loanType
 *               - formData
 *             properties:
 *               loanType:
 *                 type: string
 *                 enum: [vehicle_loan, housing_loan, personal_loan]
 *                 description: Type of loan application
 *               formData:
 *                 type: object
 *                 description: Form data using your frontend field names
 *                 example:
 *                   applicantName: "<PERSON>"
 *                   fatherName: "<PERSON>"
 *                   dateOfBirth: "1990-08-15"
 *                   age: 33
 *                   gender: "male"
 *                   maritalStatus: "married"
 *                   mobileNumber: "9876543210"
 *                   email: "<EMAIL>"
 *                   address: "123 Main Street, Mumbai"
 *                   vehicleType: "car"
 *                   vehicleMake: "Maruti"
 *                   vehicleModel: "Swift"
 *                   vehicleVariant: "VXI"
 *                   manufacturingYear: 2024
 *                   vehicleCondition: "new"
 *                   vehiclePrice: 800000
 *                   dealerName: "ABC Motors"
 *                   loanAmount: 600000
 *                   downPayment: 200000
 *                   loanTenure: "5"
 *                   interestType: "fixed"
 *                   employmentType: "salaried"
 *                   monthlyIncome: 75000
 *                   companyName: "Tech Corp"
 *                   workExperience: 8
 *                   officeAddress: "456 Business Park, Mumbai"
 *                   existingLoans: 0
 *                   bankAccount: "SBI - **********"
 *                   panNumber: "**********"
 *                   aadharNumber: "1234 5678 9012"
 *                   informationAccuracy: true
 *               isDraft:
 *                 type: boolean
 *                 default: true
 *                 description: Whether to save as draft or submit
 *     responses:
 *       201:
 *         description: Application created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     workflowId:
 *                       type: string
 *                     applicationId:
 *                       type: string
 *                     formDataId:
 *                       type: string
 *                     loanType:
 *                       type: string
 *                     loanAmount:
 *                       type: number
 *                     status:
 *                       type: string
 *                     ui:
 *                       type: object
 *                       description: Frontend UI state information
 *       400:
 *         description: Validation errors
 *       403:
 *         description: Only deputy managers can create applications
 */
router.post('/', 
  authMiddleware, 
  roleMiddleware(['deputy_manager']), 
  createApplicationFromFrontend
);

/**
 * @swagger
 * /frontend-applications/{workflowId}:
 *   put:
 *     summary: Update loan application using frontend schema format
 *     tags: [Frontend Applications]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: workflowId
 *         required: true
 *         schema:
 *           type: string
 *         description: Workflow ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - formData
 *             properties:
 *               formData:
 *                 type: object
 *                 description: Updated form data using frontend field names
 *               isDraft:
 *                 type: boolean
 *                 default: true
 *                 description: Whether to save as draft or submit
 *     responses:
 *       200:
 *         description: Application updated successfully
 *       400:
 *         description: Validation errors or cannot edit in current status
 *       403:
 *         description: Access denied
 *       404:
 *         description: Application not found
 */
router.put('/:workflowId', 
  authMiddleware, 
  roleMiddleware(['deputy_manager']), 
  updateApplicationFromFrontend
);

/**
 * @swagger
 * /frontend-applications/{workflowId}/pdf:
 *   post:
 *     summary: Generate PDF using frontend data format
 *     tags: [Frontend Applications]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: workflowId
 *         required: true
 *         schema:
 *           type: string
 *         description: Workflow ID
 *     responses:
 *       200:
 *         description: PDF generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     pdfUrl:
 *                       type: string
 *                     fileName:
 *                       type: string
 *       404:
 *         description: Application not found
 *       500:
 *         description: PDF generation failed
 */
router.post('/:workflowId/pdf', 
  authMiddleware, 
  roleMiddleware(['deputy_manager', 'branch_manager', 'general_manager', 'super_admin']), 
  generatePDFFromFrontend
);

/**
 * @swagger
 * /frontend-applications/schema/vehicle-loan:
 *   get:
 *     summary: Get vehicle loan schema in frontend format
 *     tags: [Frontend Applications]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Vehicle loan schema retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     loanCode:
 *                       type: string
 *                     title:
 *                       type: string
 *                     description:
 *                       type: string
 *                     sections:
 *                       type: array
 *                       items:
 *                         type: object
 */
router.get('/schema/vehicle-loan', 
  authMiddleware, 
  roleMiddleware(['deputy_manager', 'branch_manager', 'general_manager', 'super_admin']), 
  async (req, res) => {
    try {
      // Return your frontend schema format
      const vehicleLoanSchema = {
        loanCode: "VL",
        title: "Vehicle Loan Application",
        description: "Apply for a vehicle loan to purchase your dream car or commercial vehicle",
        subtitle: "Complete all sections accurately to ensure quick processing of your application",
        category: "Vehicle Finance",
        estimatedTime: "15-20 minutes",
        sections: [
          {
            sectionId: "applicant_details",
            sectionTitle: "Applicant Personal Information",
            description: "Personal details of the loan applicant",
            iconName: "User",
            iconColor: "text-blue-600",
            required: true,
            gridClass: "grid-cols-1 md:grid-cols-2",
            fields: [
              {
                name: "applicantName",
                label: "Full Name",
                type: "text",
                required: true,
                placeholder: "Enter your complete name",
                helpText: "Name as per official documents"
              },
              {
                name: "fatherName",
                label: "Father's Name",
                type: "text",
                required: true,
                placeholder: "Enter father's name"
              },
              {
                name: "dateOfBirth",
                label: "Date of Birth",
                type: "date",
                required: true,
                helpText: "Date of birth as per official documents"
              },
              {
                name: "age",
                label: "Age",
                type: "number",
                required: true,
                min: 18,
                max: 70,
                placeholder: "25"
              },
              {
                name: "gender",
                label: "Gender",
                type: "radio",
                options: [
                  { value: "male", label: "Male" },
                  { value: "female", label: "Female" },
                  { value: "other", label: "Other" }
                ],
                required: true
              },
              {
                name: "maritalStatus",
                label: "Marital Status",
                type: "select",
                required: true,
                options: [
                  { value: "single", label: "Single" },
                  { value: "married", label: "Married" },
                  { value: "divorced", label: "Divorced" },
                  { value: "widowed", label: "Widowed" }
                ]
              },
              {
                name: "mobileNumber",
                label: "Mobile Number",
                type: "tel",
                required: true,
                placeholder: "9876543210"
              },
              {
                name: "email",
                label: "Email Address",
                type: "email",
                required: true,
                placeholder: "<EMAIL>"
              },
              {
                name: "address",
                label: "Permanent Address",
                type: "textarea",
                required: true,
                placeholder: "Enter complete permanent address",
                rows: 3
              }
            ]
          }
          // Add other sections as needed
        ]
      };
      
      res.json({
        success: true,
        data: vehicleLoanSchema
      });
      
    } catch (error) {
      console.error('Error getting vehicle loan schema:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get schema',
        error: error.message
      });
    }
  }
);

module.exports = router;
