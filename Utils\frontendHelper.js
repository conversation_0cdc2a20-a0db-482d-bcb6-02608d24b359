const { WORKFLOW_STATUS, WORKFLOW_ACTIONS, LOAN_AMOUNT_THRESHOLD } = require('../models/loanWorkflow.model');

/**
 * Generate button configurations for frontend based on workflow state and user role
 */
const generateButtonStates = (workflow, userRole, userId) => {
  const availableActions = workflow.getAvailableActions(userRole, userId);
  const isAboveThreshold = workflow.shouldForwardToGM();
  const currentStatus = workflow.currentStatus;
  
  return {
    // Primary action buttons
    submitToBranchManager: {
      visible: availableActions.includes(WORKFLOW_ACTIONS.SUBMIT_TO_BM),
      enabled: true,
      text: 'Send to Branch Manager',
      variant: 'primary',
      icon: 'send',
      description: 'Submit application for branch manager review'
    },
    
    approve: {
      visible: availableActions.includes(WORKFLOW_ACTIONS.APPROVE_BY_BM) || 
               availableActions.includes(WORKFLOW_ACTIONS.APPROVE_BY_GM),
      enabled: userRole === 'branch_manager' ? !isAboveThreshold : true,
      text: userRole === 'branch_manager' ? 'Approve Application' : 'Final Approval',
      variant: 'success',
      icon: 'check-circle',
      description: userRole === 'branch_manager' 
        ? `Approve loan (up to ₹${(LOAN_AMOUNT_THRESHOLD).toLocaleString()})`
        : 'Give final approval for the loan'
    },
    
    forwardToGM: {
      visible: availableActions.includes(WORKFLOW_ACTIONS.FORWARD_TO_GM),
      enabled: true,
      text: 'Send to General Manager',
      variant: 'warning',
      icon: 'arrow-up',
      description: `Forward to GM (required for amounts > ₹${(LOAN_AMOUNT_THRESHOLD).toLocaleString()})`,
      required: isAboveThreshold && userRole === 'branch_manager'
    },
    
    reject: {
      visible: availableActions.includes(WORKFLOW_ACTIONS.REJECT),
      enabled: true,
      text: 'Reject Application',
      variant: 'danger',
      icon: 'x-circle',
      description: 'Reject the loan application',
      requiresComments: true
    },
    
    returnForChanges: {
      visible: availableActions.includes(WORKFLOW_ACTIONS.RETURN_FOR_CHANGES),
      enabled: true,
      text: 'Return for Changes',
      variant: 'secondary',
      icon: 'arrow-left',
      description: 'Send back to applicant for modifications',
      requiresComments: true
    },
    
    // Status indicators
    statusIndicator: {
      text: getStatusDisplayText(currentStatus),
      color: getStatusColor(currentStatus),
      icon: getStatusIcon(currentStatus)
    },
    
    // Amount-based warnings
    amountWarning: isAboveThreshold && userRole === 'branch_manager' ? {
      show: true,
      message: `This loan amount (₹${workflow.loanAmount.toLocaleString()}) exceeds your approval limit of ₹${(LOAN_AMOUNT_THRESHOLD).toLocaleString()}. You must forward it to the General Manager.`,
      type: 'warning'
    } : null
  };
};

/**
 * Get display text for workflow status
 */
const getStatusDisplayText = (status) => {
  const statusTexts = {
    [WORKFLOW_STATUS.DRAFT]: 'Draft',
    [WORKFLOW_STATUS.SUBMITTED_TO_BM]: 'Pending Branch Manager Review',
    [WORKFLOW_STATUS.BM_REVIEW]: 'Under Branch Manager Review',
    [WORKFLOW_STATUS.APPROVED_BY_BM]: 'Approved by Branch Manager',
    [WORKFLOW_STATUS.FORWARDED_TO_GM]: 'Pending General Manager Review',
    [WORKFLOW_STATUS.GM_REVIEW]: 'Under General Manager Review',
    [WORKFLOW_STATUS.APPROVED_BY_GM]: 'Approved by General Manager',
    [WORKFLOW_STATUS.REJECTED]: 'Rejected',
    [WORKFLOW_STATUS.RETURNED_FOR_CHANGES]: 'Returned for Changes'
  };
  
  return statusTexts[status] || 'Unknown Status';
};

/**
 * Get color for workflow status
 */
const getStatusColor = (status) => {
  const statusColors = {
    [WORKFLOW_STATUS.DRAFT]: 'gray',
    [WORKFLOW_STATUS.SUBMITTED_TO_BM]: 'blue',
    [WORKFLOW_STATUS.BM_REVIEW]: 'blue',
    [WORKFLOW_STATUS.APPROVED_BY_BM]: 'green',
    [WORKFLOW_STATUS.FORWARDED_TO_GM]: 'orange',
    [WORKFLOW_STATUS.GM_REVIEW]: 'orange',
    [WORKFLOW_STATUS.APPROVED_BY_GM]: 'green',
    [WORKFLOW_STATUS.REJECTED]: 'red',
    [WORKFLOW_STATUS.RETURNED_FOR_CHANGES]: 'yellow'
  };
  
  return statusColors[status] || 'gray';
};

/**
 * Get icon for workflow status
 */
const getStatusIcon = (status) => {
  const statusIcons = {
    [WORKFLOW_STATUS.DRAFT]: 'edit',
    [WORKFLOW_STATUS.SUBMITTED_TO_BM]: 'clock',
    [WORKFLOW_STATUS.BM_REVIEW]: 'eye',
    [WORKFLOW_STATUS.APPROVED_BY_BM]: 'check-circle',
    [WORKFLOW_STATUS.FORWARDED_TO_GM]: 'arrow-up',
    [WORKFLOW_STATUS.GM_REVIEW]: 'eye',
    [WORKFLOW_STATUS.APPROVED_BY_GM]: 'check-circle-2',
    [WORKFLOW_STATUS.REJECTED]: 'x-circle',
    [WORKFLOW_STATUS.RETURNED_FOR_CHANGES]: 'arrow-left'
  };
  
  return statusIcons[status] || 'help-circle';
};

/**
 * Generate workflow progress steps for frontend
 */
const generateWorkflowProgress = (workflow, userRole) => {
  const steps = [
    {
      id: 1,
      title: 'Application Created',
      description: 'Deputy Manager creates loan application',
      status: 'completed',
      icon: 'file-plus'
    },
    {
      id: 2,
      title: 'Submitted to Branch Manager',
      description: 'Application sent for branch manager review',
      status: workflow.currentStatus === WORKFLOW_STATUS.DRAFT ? 'pending' : 'completed',
      icon: 'send'
    },
    {
      id: 3,
      title: 'Branch Manager Review',
      description: workflow.shouldForwardToGM() 
        ? 'Branch Manager forwards to General Manager'
        : 'Branch Manager approves or rejects',
      status: [WORKFLOW_STATUS.SUBMITTED_TO_BM, WORKFLOW_STATUS.BM_REVIEW].includes(workflow.currentStatus) 
        ? 'current' 
        : workflow.currentStatus === WORKFLOW_STATUS.DRAFT 
        ? 'pending' 
        : 'completed',
      icon: 'user-check'
    }
  ];
  
  // Add GM step if loan amount is above threshold
  if (workflow.shouldForwardToGM()) {
    steps.push({
      id: 4,
      title: 'General Manager Review',
      description: 'Final approval from General Manager',
      status: [WORKFLOW_STATUS.FORWARDED_TO_GM, WORKFLOW_STATUS.GM_REVIEW].includes(workflow.currentStatus)
        ? 'current'
        : [WORKFLOW_STATUS.APPROVED_BY_GM].includes(workflow.currentStatus)
        ? 'completed'
        : 'pending',
      icon: 'crown'
    });
  }
  
  // Add final step
  steps.push({
    id: workflow.shouldForwardToGM() ? 5 : 4,
    title: 'Final Decision',
    description: 'Application approved or rejected',
    status: [WORKFLOW_STATUS.APPROVED_BY_BM, WORKFLOW_STATUS.APPROVED_BY_GM].includes(workflow.currentStatus)
      ? 'completed'
      : workflow.currentStatus === WORKFLOW_STATUS.REJECTED
      ? 'rejected'
      : 'pending',
    icon: workflow.currentStatus === WORKFLOW_STATUS.REJECTED ? 'x-circle' : 'check-circle'
  });
  
  return steps;
};

/**
 * Generate notification messages for different workflow states
 */
const generateNotifications = (workflow, userRole, userId) => {
  const notifications = [];
  
  // Check if user is the current reviewer
  if (workflow.currentReviewer && workflow.currentReviewer.toString() === userId.toString()) {
    notifications.push({
      type: 'info',
      title: 'Action Required',
      message: 'This application is assigned to you for review.',
      priority: 'high'
    });
  }
  
  // Amount-based notifications
  if (workflow.shouldForwardToGM() && userRole === 'branch_manager') {
    notifications.push({
      type: 'warning',
      title: 'High Value Loan',
      message: `This loan amount (₹${workflow.loanAmount.toLocaleString()}) requires General Manager approval.`,
      priority: 'medium'
    });
  }
  
  // Status-based notifications
  if (workflow.currentStatus === WORKFLOW_STATUS.RETURNED_FOR_CHANGES && userRole === 'deputy_manager') {
    notifications.push({
      type: 'warning',
      title: 'Changes Required',
      message: 'This application has been returned for modifications.',
      priority: 'high'
    });
  }
  
  if (workflow.currentStatus === WORKFLOW_STATUS.REJECTED) {
    notifications.push({
      type: 'error',
      title: 'Application Rejected',
      message: workflow.rejectionReason || 'Application has been rejected.',
      priority: 'high'
    });
  }
  
  return notifications;
};

/**
 * Generate complete frontend response
 */
const generateFrontendResponse = (workflow, userRole, userId, includeHistory = true) => {
  const buttonStates = generateButtonStates(workflow, userRole, userId);
  const progressSteps = generateWorkflowProgress(workflow, userRole);
  const notifications = generateNotifications(workflow, userRole, userId);
  
  const response = {
    workflow: {
      id: workflow._id,
      applicationId: workflow.applicationId,
      currentStatus: workflow.currentStatus,
      loanAmount: workflow.loanAmount,
      isAboveThreshold: workflow.shouldForwardToGM(),
      threshold: LOAN_AMOUNT_THRESHOLD,
      currentReviewer: workflow.currentReviewer,
      createdBy: workflow.createdBy,
      branch: workflow.branch,
      rejectionReason: workflow.rejectionReason
    },
    ui: {
      buttons: buttonStates,
      progress: progressSteps,
      notifications,
      availableActions: workflow.getAvailableActions(userRole, userId)
    },
    metadata: {
      canEdit: [WORKFLOW_STATUS.DRAFT, WORKFLOW_STATUS.RETURNED_FOR_CHANGES].includes(workflow.currentStatus) && userRole === 'deputy_manager',
      canView: true,
      isOwner: workflow.createdBy.toString() === userId.toString(),
      isReviewer: workflow.currentReviewer && workflow.currentReviewer.toString() === userId.toString()
    }
  };
  
  if (includeHistory) {
    response.workflow.history = workflow.workflowHistory;
  }
  
  return response;
};

module.exports = {
  generateButtonStates,
  generateWorkflowProgress,
  generateNotifications,
  generateFrontendResponse,
  getStatusDisplayText,
  getStatusColor,
  getStatusIcon
};
