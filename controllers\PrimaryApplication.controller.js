const { PrimaryApplication } = require("../schema/primaryApplication.model");
const Applicant = PrimaryApplication;
const loadDetails = require("../schema/loanDetails.model");
const CollateralDetails = require("../schema/collateral.model");
const documentDetails = require("../schema/documentDetails.model");
const mongoose = require("mongoose");
const { mongooseValidateError, paginateModel } = require("../utils");
const loanType = require("../schema/loanType");
const { ROLES, STATUS, ACTIONS } = require("../constants/roles");
const { ErrorHandler } = require("../utils/common");
const User = require("../models/user.model");

const createPrimaryApplication = async (req, res, next) => {
  const session = await mongoose.startSession();
  session.startTransaction();

  try {
    const applicantData = req.body;
    const { role, userId, branch } = req.user;

    // Check if user has permission to create application
    if (
      ![
        ROLES.DEPUTY_MANAGER,
        ROLES.BRANCH_MANAGER,
        ROLES.GENERAL_MANAGER,
        ROLES.SUPER_ADMIN,
      ].includes(role)
    ) {
      throw new ErrorHandler(
        "You do not have permission to create applications",
        403
      );
    }

    // For deputy managers, ensure they are assigned to a branch
    if (role === ROLES.DEPUTY_MANAGER && !branch) {
      throw new ErrorHandler(
        "Deputy managers must be assigned to a branch",
        400
      );
    }

    // Create new application
    const newApplication = new Applicant({
      ...applicantData,
      status: STATUS.DRAFT,
      createdBy: userId,
      branch: branch || applicantData.branch,
      currentStatus: {
        status: STATUS.DRAFT,
        updatedBy: userId,
        updatedAt: new Date(),
      },
      workflow: [
        {
          fromStatus: null,
          toStatus: STATUS.DRAFT,
          action: ACTIONS.CREATE,
          performedBy: userId,
          comments: "Application created",
          timestamp: new Date(),
        },
      ],
    });

    // Save application
    const savedApplication = await newApplication.save({ session });

    await session.commitTransaction();
    session.endSession();

    res.status(201).json({
      success: true,
      message: "Application created successfully",
      data: savedApplication,
    });
  } catch (error) {
    await session.abortTransaction();
    session.endSession();

    // Handle validation errors
    if (error instanceof mongoose.Error.ValidationError) {
      return res.status(400).json({
        success: false,
        message: "Validation Error",
        error: mongooseValidateError(error),
      });
    }

    // Handle other errors
    next(error);
  }
};

const transitionPrimaryApplicationStatus = async (req, res, next) => {
  const session = await mongoose.startSession();
  session.startTransaction();

  try {
    const { id } = req.params;
    const { action, comments = "" } = req.body;
    const { role, userId, branch } = req.user;

    if (!id || !action) {
      throw new ErrorHandler("Application ID and action are required", 400);
    }

    // Find the application
    const application = await PrimaryApplication.findById(id).session(session);
    if (!application) {
      throw new ErrorHandler("Application not found", 404);
    }

    // Check branch access
    if (
      [ROLES.BRANCH_MANAGER, ROLES.DEPUTY_MANAGER].includes(role) &&
      application.branch.toString() !== branch.toString()
    ) {
      throw new ErrorHandler("Not authorized to modify this application", 403);
    }

    // Determine next status based on current status and action
    let nextStatus;
    const currentStatus = application.status;

    switch (action) {
      case ACTIONS.SUBMIT:
        if (![ROLES.DEPUTY_MANAGER, ROLES.BRANCH_MANAGER].includes(role)) {
          throw new ErrorHandler(
            "You do not have permission to submit applications",
            403
          );
        }
        nextStatus = STATUS.PENDING_APPROVAL;
        break;

      case ACTIONS.APPROVE:
        if (
          ![
            ROLES.BRANCH_MANAGER,
            ROLES.GENERAL_MANAGER,
            ROLES.SUPER_ADMIN,
          ].includes(role)
        ) {
          throw new ErrorHandler(
            "You do not have permission to approve applications",
            403
          );
        }
        nextStatus = STATUS.APPROVED;
        break;

      case ACTIONS.REJECT:
        if (
          ![
            ROLES.BRANCH_MANAGER,
            ROLES.GENERAL_MANAGER,
            ROLES.SUPER_ADMIN,
          ].includes(role)
        ) {
          throw new ErrorHandler(
            "You do not have permission to reject applications",
            403
          );
        }
        nextStatus = STATUS.REJECTED;
        break;

      case ACTIONS.RETURN:
        if (
          ![
            ROLES.BRANCH_MANAGER,
            ROLES.GENERAL_MANAGER,
            ROLES.SUPER_ADMIN,
          ].includes(role)
        ) {
          throw new ErrorHandler(
            "You do not have permission to return applications",
            403
          );
        }
        nextStatus = STATUS.DRAFT;
        break;

      case ACTIONS.DISBURSE:
        if (![ROLES.GENERAL_MANAGER, ROLES.SUPER_ADMIN].includes(role)) {
          throw new ErrorHandler(
            "You do not have permission to disburse loans",
            403
          );
        }
        if (application.status !== STATUS.APPROVED) {
          throw new ErrorHandler(
            "Only approved applications can be disbursed",
            400
          );
        }
        nextStatus = STATUS.DISBURSED;
        break;

      default:
        throw new ErrorHandler("Invalid action", 400);
    }

    // Update application status and workflow
    application.status = nextStatus;
    application.currentStatus = {
      status: nextStatus,
      updatedBy: userId,
      updatedAt: new Date(),
    };

    application.workflow.push({
      fromStatus: currentStatus,
      toStatus: nextStatus,
      action,
      performedBy: userId,
      comments,
      timestamp: new Date(),
    });

    // Save the updated application
    const updatedApplication = await application.save({ session });

    await session.commitTransaction();
    session.endSession();

    res.json({
      success: true,
      message: `Application ${action}d successfully`,
      data: updatedApplication,
    });
  } catch (error) {
    await session.abortTransaction();
    session.endSession();
    next(error);
  }
};

const saveLoanDetails = async (req, res) => {
  try {
    const { primaryApplicationId, ...loanData } = req.body;

    if (!primaryApplicationId) {
      return res.status(400).json({
        success: false,
        message: "ApplicationId is Required",
      });
    }

    const loanDetailsInstance = new loadDetails({
      primaryApplicationId,
      ...loanData,
    });

    const savedLoadDetails = await loanDetailsInstance.save();

    res.status(200).json({
      success: true,
      data: savedLoadDetails,
    });
  } catch (error) {
    // Handle validation errors
    if (mongooseValidateError(res, error)) return;

    console.log(error);

    // Handle other errors
    res.status(500).json({
      success: false,
      message: "Server Error",
      error: error.message,
    });
  }
};

const saveCollateralDetails = async (req, res) => {
  try {
    const {
      primaryApplicationId,
      propertyOwnerName,
      propertyAddress,
      propertyType,
      landArea,
      constructionArea,
      propertyValuation,
      valuationDate,
    } = req.body;

    // Optionally validate required fields here
    if (!primaryApplicationId || !propertyOwnerName) {
      return res.status(400).json({ error: "Missing required fields" });
    }

    // Create and save the collateral details
    const collateral = await CollateralDetails.create({
      primaryApplicationId,
      propertyOwnerName,
      propertyAddress,
      propertyType,
      landArea,
      constructionArea,
      propertyValuation,
      valuationDate,
    });

    return res.status(200).json({
      message: "Collateral details saved successfully",
      data: collateral,
    });
  } catch (error) {
    console.error("Error saving collateral details:", error);

    // Ensure headers are not already sent
    if (!res.headersSent) {
      return res.status(500).json({ error: "Internal Server Error" });
    }
  }
};

const saveDocumentDetails = async (req, res) => {
  try {
    const { primaryApplicationId, ...documentData } = req.body;

    if (!primaryApplicationId) {
      return res.status(400).json({
        success: false,
        message: "ApplicationId is Required",
      });
    }

    const documentDetailsInstance = new documentDetails({
      primaryApplicationId,
      ...documentData,
    });

    const savedDocumentDetails = await documentDetailsInstance.save();

    res.status(200).json({
      success: true,
      data: savedDocumentDetails,
    });
  } catch (error) {
    // Handle validation errors
    if (mongooseValidateError(res, error)) return;

    console.log(error);

    // Handle other errors
    res.status(500).json({
      success: false,
      message: "Server Error",
      error: error.message,
    });
  }
};

const getPrimaryApplication = async (req, res) => {
  try {
    const { primaryApplicationId } = req.params;

    if (!primaryApplicationId) {
      return res.status(400).json({
        success: false,
        message: "Primary Application ID is required",
      });
    }

    // Fetch the primary application
    const primaryApplication = await Applicant.findById(primaryApplicationId);

    if (!primaryApplication) {
      return res
        .status(404)
        .json({ success: false, message: "Primary Application not found" });
    }

    // Fetch related data using the same primaryApplicationId
    const [loanData, collateralData, documentData] = await Promise.all([
      loadDetails.find({ primaryApplicationId }),
      CollateralDetails.find({ primaryApplicationId }),
      documentDetails.find({ primaryApplicationId }),
    ]);

    return res.status(200).json({
      success: true,
      data: {
        primaryApplication,
        loanData,
        collateralData,
        documentData,
      },
    });
  } catch (error) {
    console.error("Error fetching primary application details:", error);
    return res
      .status(500)
      .json({ success: false, message: "Server Error", error: error.message });
  }
};

const getLoanType = async (req, res) => {
  try {
    const loanData = await loanType.find();
    console.log("loanData -->", loanData);
    res.status(200).json({ success: true, data: loanData });
  } catch (error) {
    console.error("Error fetching loanData:", error);
    res.status(500).json({ message: "Server Error" });
  }
};

const getPrimaryApplicationList = async (req, res) => {
  try {
    const { status, page = 1, limit = 10 } = req.query;

    const filter = {};
    if (status !== undefined) {
      filter.status = parseInt(status); // Ensuring number type
    }

    // Manual pagination for better control
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const skip = (pageNum - 1) * limitNum;

    const [data, total] = await Promise.all([
      Applicant.find(filter)
        .skip(skip)
        .limit(limitNum)
        .sort({ createdAt: -1 })
        .lean(), // Use lean() for better performance
      Applicant.countDocuments(filter),
    ]);

    res.status(200).json({
      success: true,
      data: data,
      pagination: {
        currentPage: pageNum,
        totalPages: Math.ceil(total / limitNum),
        totalItems: total,
      },
    });
  } catch (error) {
    console.error("Error in getPrimaryApplicationList:", error);
    res.status(500).json({
      success: false,
      message: "Server Error",
      error: process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }
};

const testPrimaryApp = async (req, res) => {
  try {
    const payload = req.body;

    const testApplicantData = new testApplicant(payload);

    const savedtestApplicant = await testApplicantData.save();

    console.log("payload -->", payload);

    res.status(500).json({
      success: true,
      data: savedtestApplicant,
    });
  } catch (error) {
    // Handle validation errors
    if (mongooseValidateError(res, error)) return;

    // Handle other errors
    res.status(500).json({
      success: false,
      message: "Server Error",
      error: error.message,
    });
  }
};

module.exports = {
  createPrimaryApplication,
  saveLoanDetails,
  saveCollateralDetails,
  saveDocumentDetails,
  getPrimaryApplication,
  testPrimaryApp,
  getLoanType,
  transitionPrimaryApplicationStatus,
  getPrimaryApplicationList,
};
