const mongoose = require('mongoose');

// Enhanced Loan Workflow Constants
const WORKFLOW_STATUS = {
  DRAFT: 'draft',
  SUBMITTED_TO_BM: 'submitted_to_branch_manager',
  BM_REVIEW: 'branch_manager_review',
  APPROVED_BY_BM: 'approved_by_branch_manager',
  FORWARDED_TO_GM: 'forwarded_to_general_manager',
  GM_REVIEW: 'general_manager_review',
  APPROVED_BY_GM: 'approved_by_general_manager',
  REJECTED: 'rejected',
  RETURNED_FOR_CHANGES: 'returned_for_changes'
};

const WORKFLOW_ACTIONS = {
  SAVE_DRAFT: 'save_draft',
  SUBMIT_TO_BM: 'submit_to_branch_manager',
  APPROVE_BY_BM: 'approve_by_branch_manager',
  FOR<PERSON>RD_TO_GM: 'forward_to_general_manager',
  APPROVE_BY_GM: 'approve_by_general_manager',
  REJECT: 'reject',
  RETURN_FOR_CHANGES: 'return_for_changes'
};

const LOAN_AMOUNT_THRESHOLD = 2500000; // 25 Lakhs in rupees

// Workflow transition rules
const WOR<PERSON>FLOW_TRANSITIONS = {
  [WORKFLOW_STATUS.DRAFT]: {
    allowedActions: [WORKFLOW_ACTIONS.SAVE_DRAFT, WORKFLOW_ACTIONS.SUBMIT_TO_BM],
    allowedRoles: ['deputy_manager'],
    nextStatus: {
      [WORKFLOW_ACTIONS.SAVE_DRAFT]: WORKFLOW_STATUS.DRAFT,
      [WORKFLOW_ACTIONS.SUBMIT_TO_BM]: WORKFLOW_STATUS.SUBMITTED_TO_BM
    }
  },
  [WORKFLOW_STATUS.SUBMITTED_TO_BM]: {
    allowedActions: [WORKFLOW_ACTIONS.APPROVE_BY_BM, WORKFLOW_ACTIONS.FORWARD_TO_GM, WORKFLOW_ACTIONS.REJECT, WORKFLOW_ACTIONS.RETURN_FOR_CHANGES],
    allowedRoles: ['branch_manager'],
    nextStatus: {
      [WORKFLOW_ACTIONS.APPROVE_BY_BM]: WORKFLOW_STATUS.APPROVED_BY_BM,
      [WORKFLOW_ACTIONS.FORWARD_TO_GM]: WORKFLOW_STATUS.FORWARDED_TO_GM,
      [WORKFLOW_ACTIONS.REJECT]: WORKFLOW_STATUS.REJECTED,
      [WORKFLOW_ACTIONS.RETURN_FOR_CHANGES]: WORKFLOW_STATUS.RETURNED_FOR_CHANGES
    }
  },
  [WORKFLOW_STATUS.FORWARDED_TO_GM]: {
    allowedActions: [WORKFLOW_ACTIONS.APPROVE_BY_GM, WORKFLOW_ACTIONS.REJECT, WORKFLOW_ACTIONS.RETURN_FOR_CHANGES],
    allowedRoles: ['general_manager'],
    nextStatus: {
      [WORKFLOW_ACTIONS.APPROVE_BY_GM]: WORKFLOW_STATUS.APPROVED_BY_GM,
      [WORKFLOW_ACTIONS.REJECT]: WORKFLOW_STATUS.REJECTED,
      [WORKFLOW_ACTIONS.RETURN_FOR_CHANGES]: WORKFLOW_STATUS.RETURNED_FOR_CHANGES
    }
  },
  [WORKFLOW_STATUS.RETURNED_FOR_CHANGES]: {
    allowedActions: [WORKFLOW_ACTIONS.SAVE_DRAFT, WORKFLOW_ACTIONS.SUBMIT_TO_BM],
    allowedRoles: ['deputy_manager'],
    nextStatus: {
      [WORKFLOW_ACTIONS.SAVE_DRAFT]: WORKFLOW_STATUS.DRAFT,
      [WORKFLOW_ACTIONS.SUBMIT_TO_BM]: WORKFLOW_STATUS.SUBMITTED_TO_BM
    }
  }
};

// Workflow History Schema
const WorkflowHistorySchema = new mongoose.Schema({
  action: {
    type: String,
    enum: Object.values(WORKFLOW_ACTIONS),
    required: true
  },
  fromStatus: {
    type: String,
    enum: Object.values(WORKFLOW_STATUS),
    required: true
  },
  toStatus: {
    type: String,
    enum: Object.values(WORKFLOW_STATUS),
    required: true
  },
  performedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  performedByRole: {
    type: String,
    enum: ['deputy_manager', 'branch_manager', 'general_manager', 'super_admin'],
    required: true
  },
  comments: {
    type: String,
    trim: true
  },
  loanAmount: {
    type: Number,
    required: true
  },
  timestamp: {
    type: Date,
    default: Date.now
  }
});

// Enhanced Primary Application Schema with Workflow
const LoanWorkflowSchema = new mongoose.Schema({
  applicationId: {
    type: String,
    unique: true,
    required: true
  },
  
  // Current workflow state
  currentStatus: {
    type: String,
    enum: Object.values(WORKFLOW_STATUS),
    default: WORKFLOW_STATUS.DRAFT,
    required: true
  },
  
  // Current reviewer (who should act next)
  currentReviewer: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: false
  },
  
  // Loan amount for workflow routing
  loanAmount: {
    type: Number,
    required: true,
    min: [1, 'Loan amount must be positive']
  },
  
  // Workflow history
  workflowHistory: [WorkflowHistorySchema],
  
  // Application creator
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  
  // Branch information
  branch: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Branch',
    required: true
  },
  
  // Application data reference
  applicationData: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'PrimaryApplication',
    required: true
  },
  
  // Rejection/Return reason
  rejectionReason: {
    type: String,
    trim: true
  },
  
  // System fields
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Pre-save middleware to generate application ID
LoanWorkflowSchema.pre('save', async function(next) {
  if (this.isNew && !this.applicationId) {
    const count = await this.constructor.countDocuments();
    this.applicationId = `LWF-${(count + 1).toString().padStart(6, '0')}`;
  }
  next();
});

// Method to check if action is allowed for current user and status
LoanWorkflowSchema.methods.canPerformAction = function(action, userRole, userId) {
  const currentTransition = WORKFLOW_TRANSITIONS[this.currentStatus];
  
  if (!currentTransition) {
    return { allowed: false, reason: 'Invalid current status' };
  }
  
  // Check if action is allowed for current status
  if (!currentTransition.allowedActions.includes(action)) {
    return { allowed: false, reason: 'Action not allowed for current status' };
  }
  
  // Check if user role is allowed
  if (!currentTransition.allowedRoles.includes(userRole)) {
    return { allowed: false, reason: 'User role not authorized for this action' };
  }
  
  // For branch manager, check if they are the current reviewer or if no reviewer is set
  if (userRole === 'branch_manager' && this.currentReviewer && 
      this.currentReviewer.toString() !== userId.toString()) {
    return { allowed: false, reason: 'Not the assigned reviewer' };
  }
  
  return { allowed: true };
};

// Method to get next status for an action
LoanWorkflowSchema.methods.getNextStatus = function(action) {
  const currentTransition = WORKFLOW_TRANSITIONS[this.currentStatus];
  return currentTransition?.nextStatus[action] || this.currentStatus;
};

// Method to determine if loan should go to GM based on amount
LoanWorkflowSchema.methods.shouldForwardToGM = function() {
  return this.loanAmount > LOAN_AMOUNT_THRESHOLD;
};

// Method to get available actions for current user
LoanWorkflowSchema.methods.getAvailableActions = function(userRole, userId) {
  const canPerform = this.canPerformAction.bind(this);
  const currentTransition = WORKFLOW_TRANSITIONS[this.currentStatus];
  
  if (!currentTransition) return [];
  
  return currentTransition.allowedActions.filter(action => {
    const result = canPerform(action, userRole, userId);
    return result.allowed;
  });
};

// Static method to get workflow constants
LoanWorkflowSchema.statics.getConstants = function() {
  return {
    WORKFLOW_STATUS,
    WORKFLOW_ACTIONS,
    LOAN_AMOUNT_THRESHOLD,
    WORKFLOW_TRANSITIONS
  };
};

const LoanWorkflow = mongoose.model('LoanWorkflow', LoanWorkflowSchema);

module.exports = {
  LoanWorkflow,
  WORKFLOW_STATUS,
  WORKFLOW_ACTIONS,
  LOAN_AMOUNT_THRESHOLD,
  WORKFLOW_TRANSITIONS
};
