const mongoose = require('mongoose');

const branchSchema = new mongoose.Schema({
    // Basic Information
    name: {
        type: String,
        required: [true, 'Branch name is required'],
        trim: true,
        maxlength: [100, 'Branch name cannot be longer than 100 characters']
    },
    code: {
        type: String,
        required: [true, 'Branch code is required'],
        unique: true,
        trim: true,
        uppercase: true,
        maxlength: [10, 'Branch code cannot be longer than 10 characters']
    },
    
    // Contact Information
    email: {
        type: String,
        required: [true, 'Email is required'],
        trim: true,
        lowercase: true,
        match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please provide a valid email']
    },
    phone: {
        type: String,
        required: [true, 'Phone number is required'],
        match: [/^[0-9]{10,15}$/, 'Please provide a valid phone number']
    },
    
    // Address Information
    address: {
        street: {
            type: String,
            required: [true, 'Street address is required']
        },
        city: {
            type: String,
            required: [true, 'City is required']
        },
        state: {
            type: String,
            required: [true, 'State is required']
        },
        postalCode: {
            type: String,
            required: [true, 'Postal code is required']
        },
        country: {
            type: String,
            required: [true, 'Country is required'],
            default: 'India'
        },
        landmark: {
            type: String,
            required: false
        }
    },
    
    // Branch Details
    ifscCode: {
        type: String,
        required: [true, 'IFSC code is required'],
        uppercase: true,
        match: [/^[A-Z]{4}0[A-Z0-9]{6}$/, 'Please provide a valid IFSC code']
    },
    micrCode: {
        type: String,
        required: [true, 'MICR code is required'],
        match: [/^[0-9]{9}$/, 'Please provide a valid 9-digit MICR code']
    },
    branchManager: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: false
    },
    
    // Operational Details
    openingDate: {
        type: Date,
        required: [true, 'Opening date is required'],
        default: Date.now
    },
    isActive: {
        type: Boolean,
        default: true
    },
    
    // System Fields
    createdAt: {
        type: Date,
        default: Date.now
    },
    updatedAt: {
        type: Date,
        default: Date.now
    },
    createdBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: false
    },
    updatedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: false
    }
});

// Update updatedAt field before saving
branchSchema.pre('save', function(next) {
    this.updatedAt = new Date();
    next();
});

// Virtual for formatted address
branchSchema.virtual('formattedAddress').get(function() {
    const addr = this.address;
    return `${addr.street}, ${addr.city}, ${addr.state} ${addr.postalCode}, ${addr.country}`;
});

// Create model
const Branch = mongoose.model('Branch', branchSchema);

module.exports = Branch;
