const mongoose = require("mongoose");
const Schema = mongoose.Schema;

const userSchema = new Schema(
    {
        branchId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: "Branch",
            required: true,
        },
        firstName: { type: String, required: true },
        middleName: { type: String },
        lastName: { type: String, required: true },
        email: { type: String, require: true, unique: true },
        phoneCode: { type: String, require: true, },
        phoneNumber: { type: String, require: true, },
        isDeleted: {
            type: Boolean,
            default: false,
        },
        role: { type: string, required: true }

    },
    { timestamps: true }
);

module.exports = mongoose.model("User", userSchema);
