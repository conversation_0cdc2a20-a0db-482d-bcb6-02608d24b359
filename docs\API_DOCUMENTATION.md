# API Documentation - Recent Updates

## Overview
This document covers the recently updated API endpoints for Products and Branches, including fixes for pagination, response formats, and route handling.

## Base URL
```
http://localhost:3001
```

## Authentication
All endpoints require Bearer token authentication unless specified otherwise.

```http
Authorization: Bearer <your-jwt-token>
```

---

## Product APIs

### 1. Get All Products (Paginated)

**Endpoints:**
- `GET /product/` 
- `GET /product/get` *(Alternative route for frontend compatibility)*

**Description:** Retrieve all products with pagination support.

**Query Parameters:**
| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| page | integer | 1 | Page number |
| limit | integer | 10 | Number of items per page |

**Request Example:**
```http
GET /product/get?page=1&limit=10
Authorization: Bearer <token>
```

**Response Format:**
```json
{
    "docs": [
        {
            "_id": "64f8a1b2c3d4e5f6a7b8c9d0",
            "loanTypeId": {
                "_id": "64f8a1b2c3d4e5f6a7b8c9d1",
                "loanCode": "VL",
                "loanName": "Vehicle Loan"
            },
            "productCode": "LOAN_VEHICLE",
            "minAmount": "100000",
            "maxAmount": "20000000",
            "timePeriod": "10",
            "interestRate": "9",
            "isJoint": true,
            "productStatus": "active",
            "isDeleted": false,
            "createdAt": "2023-09-06T10:30:00.000Z",
            "updatedAt": "2023-09-06T10:30:00.000Z"
        }
    ],
    "total": 1,
    "limit": 10,
    "page": 1,
    "totalPages": 1,
    "hasNextPage": false,
    "hasPreviousPage": false,
    "nextPage": null,
    "prevPage": null
}
```

**Status Codes:**
- `200` - Success
- `401` - Unauthorized
- `500` - Internal Server Error

---

### 2. Get Products by Loan Type

**Endpoint:** `GET /product/loan-type/:loanTypeId`

**Description:** Retrieve products filtered by loan type with pagination.

**Path Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| loanTypeId | string | Yes | MongoDB ObjectId of the loan type |

**Query Parameters:**
| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| page | integer | 1 | Page number |
| limit | integer | 10 | Number of items per page |

**Request Example:**
```http
GET /product/loan-type/64f8a1b2c3d4e5f6a7b8c9d1?page=1&limit=5
Authorization: Bearer <token>
```

**Response Format:**
```json
{
    "success": true,
    "data": {
        "docs": [...],
        "total": 5,
        "limit": 5,
        "page": 1,
        "totalPages": 1,
        "hasNextPage": false,
        "hasPreviousPage": false,
        "nextPage": null,
        "prevPage": null
    }
}
```

---

### 3. Create Product

**Endpoint:** `POST /product/`

**Description:** Create a new product (Super Admin only).

**Required Role:** `superadmin`

**Request Body:**
```json
{
    "loanTypeId": "64f8a1b2c3d4e5f6a7b8c9d1",
    "productCode": "LOAN_VEHICLE",
    "minAmount": "100000",
    "maxAmount": "20000000",
    "timePeriod": "10",
    "interestRate": "9",
    "isJoint": true,
    "productStatus": "active"
}
```

**Validation Rules:**
- `loanTypeId`: Required, must be valid ObjectId
- `productCode`: Required, must match loan type code
- `minAmount`: Required
- `maxAmount`: Required
- `timePeriod`: Required
- `interestRate`: Required
- `isJoint`: Required boolean
- `productStatus`: Optional, enum: ['active', 'inactive']

**Response:**
```json
{
    "message": "Product created successfully",
    "product": {
        "_id": "64f8a1b2c3d4e5f6a7b8c9d0",
        "loanTypeId": "64f8a1b2c3d4e5f6a7b8c9d1",
        "productCode": "LOAN_VEHICLE",
        "minAmount": "100000",
        "maxAmount": "20000000",
        "timePeriod": "10",
        "interestRate": "9",
        "isJoint": true,
        "productStatus": "active",
        "isDeleted": false,
        "createdAt": "2023-09-06T10:30:00.000Z",
        "updatedAt": "2023-09-06T10:30:00.000Z"
    }
}
```

---

## Branch APIs

### 1. Get All Branches (Paginated)

**Endpoint:** `GET /branch/get`

**Description:** Retrieve branches with role-based filtering and pagination.

**Access Control:**
- **Super Admin/Superadmin**: Can view all branches
- **Other Roles**: Can only view their assigned branch

**Query Parameters:**
| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| page | integer | 1 | Page number |
| limit | integer | 10 | Number of items per page |

**Request Example:**
```http
GET /branch/get?page=1&limit=10
Authorization: Bearer <token>
```

**Response Format:**
```json
{
    "docs": [
        {
            "_id": "64f8a1b2c3d4e5f6a7b8c9d2",
            "name": "Head Office",
            "code": "HO",
            "email": "<EMAIL>",
            "phone": "**********",
            "address": {
                "street": "123 Main Street",
                "city": "Mumbai",
                "state": "Maharashtra",
                "postalCode": "400001",
                "country": "India"
            },
            "ifscCode": "BANK0000001",
            "micrCode": "*********",
            "isActive": true,
            "createdAt": "2023-09-06T10:30:00.000Z",
            "updatedAt": "2023-09-06T10:30:00.000Z"
        }
    ],
    "total": 1,
    "limit": 10,
    "page": 1,
    "totalPages": 1,
    "hasNextPage": false,
    "hasPreviousPage": false,
    "nextPage": null,
    "prevPage": null
}
```

**Status Codes:**
- `200` - Success
- `401` - Unauthorized
- `403` - Forbidden (insufficient permissions)
- `500` - Internal Server Error

---

### 2. Get Branch by ID

**Endpoint:** `GET /branch/get/:id`

**Description:** Retrieve a specific branch by ID.

**Path Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| id | string | Yes | MongoDB ObjectId of the branch |

**Request Example:**
```http
GET /branch/get/64f8a1b2c3d4e5f6a7b8c9d2
Authorization: Bearer <token>
```

---

### 3. Create Branch

**Endpoint:** `POST /branch/create`

**Description:** Create a new branch (Super Admin only).

**Required Role:** `super_admin` or `superadmin`

**Request Body:**
```json
{
    "name": "New Branch",
    "code": "NB",
    "email": "<EMAIL>",
    "phone": "**********",
    "address": {
        "street": "456 New Street",
        "city": "Delhi",
        "state": "Delhi",
        "postalCode": "110001",
        "country": "India"
    },
    "ifscCode": "BANK0000002",
    "micrCode": "*********"
}
```

---

## Common Response Formats

### Success Response
```json
{
    "success": true,
    "data": { ... },
    "message": "Operation completed successfully"
}
```

### Error Response
```json
{
    "success": false,
    "message": "Error description",
    "error": "Detailed error message (development only)"
}
```

### Pagination Response Structure
```json
{
    "docs": [...],           // Array of documents
    "total": 25,             // Total number of documents
    "limit": 10,             // Items per page
    "page": 1,               // Current page
    "totalPages": 3,         // Total number of pages
    "hasNextPage": true,     // Boolean indicating if next page exists
    "hasPreviousPage": false, // Boolean indicating if previous page exists
    "nextPage": 2,           // Next page number (null if no next page)
    "prevPage": null         // Previous page number (null if no previous page)
}
```

---

## Recent Changes & Fixes

### Product API Updates:
1. **Added Alternative Route**: `/product/get` for frontend compatibility
2. **Fixed Pagination**: Updated to use correct `paginateModel` function from `Utilss/common.js`
3. **Standardized Response**: Unified response format across all endpoints
4. **Added Population**: Product responses now include populated `loanTypeId` data
5. **Fixed Import Issues**: Resolved missing `getProductsByLoanType` import

### Branch API Updates:
1. **Resolved Model Conflict**: Fixed duplicate Branch model definitions
2. **Updated Filtering**: Changed from `isDeleted` to `isActive` filtering
3. **Fixed Pagination**: Updated to use correct pagination utility
4. **Standardized Response**: Unified response format
5. **Enhanced Role-Based Access**: Improved access control logic

### Technical Improvements:
1. **Error Handling**: Better error messages and status codes
2. **Debug Logging**: Added console logs for troubleshooting
3. **Model Consistency**: Ensured single source of truth for models
4. **Response Standardization**: Consistent pagination format across APIs

---

## Testing

### Sample cURL Commands:

**Get Products:**
```bash
curl -X GET "http://localhost:3001/product/get?page=1&limit=10" \
  -H "Authorization: Bearer <your-token>"
```

**Get Branches:**
```bash
curl -X GET "http://localhost:3001/branch/get?page=1&limit=10" \
  -H "Authorization: Bearer <your-token>"
```

**Create Product:**
```bash
curl -X POST "http://localhost:3001/product/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <your-token>" \
  -d '{
    "loanTypeId": "64f8a1b2c3d4e5f6a7b8c9d1",
    "productCode": "LOAN_VEHICLE",
    "minAmount": "100000",
    "maxAmount": "20000000",
    "timePeriod": "10",
    "interestRate": "9",
    "isJoint": true,
    "productStatus": "active"
  }'
```

---

## Notes

1. **Authentication**: All endpoints require valid JWT token
2. **Permissions**: Role-based access control is enforced
3. **Validation**: Request data is validated according to schema rules
4. **Pagination**: Default page size is 10, maximum recommended is 100
5. **Population**: Related data is automatically populated in responses
6. **Filtering**: Only active/non-deleted records are returned by default

For additional endpoints or detailed error codes, please refer to the Swagger documentation at `/api-docs` when the server is running.
