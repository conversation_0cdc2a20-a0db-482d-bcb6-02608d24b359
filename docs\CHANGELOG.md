# Changelog

All notable changes to the LOS Backend API will be documented in this file.

## [1.2.0] - 2024-01-XX

### 🚀 Added
- **Alternative Product Route**: Added `/product/get` endpoint for frontend compatibility
- **Enhanced Swagger Documentation**: Comprehensive API documentation with request/response schemas
- **Debug Logging**: Added console logging for troubleshooting pagination and database queries
- **Schema Definitions**: Created detailed Swagger schemas for Product and Branch models
- **Comprehensive API Documentation**: Created detailed API documentation in `docs/API_DOCUMENTATION.md`

### 🔧 Fixed
- **Critical Route Handler Error**: Fixed `Route.post() requires a callback function but got a [object Undefined]` error
  - **Root Cause**: Missing `getProductsByLoanType` import in `routers/product.router.js`
  - **Solution**: Added missing import from product controller
- **Mongoose Model Conflict**: Resolved `OverwriteModelError: Cannot overwrite 'Branch' model once compiled`
  - **Root Cause**: Duplicate Branch model definitions in `models/branch.model.js` and `schema/branch.model.js`
  - **Solution**: Renamed conflicting schema file to `schema/branch-legacy.model.js`
- **Pagination Function Mismatch**: Fixed incorrect `paginateModel` function usage
  - **Root Cause**: Controllers using wrong pagination utility with incompatible signatures
  - **Solution**: Updated imports to use `Utilss/common.js` version with proper signature
- **Response Format Inconsistency**: Standardized pagination response format across all endpoints
  - **Root Cause**: Different pagination utilities returning different response structures
  - **Solution**: Implemented consistent response transformation in controllers

### 🔄 Changed
- **Product Controller**: 
  - Updated import to use correct `paginateModel` from `Utilss/common.js`
  - Standardized response format with `docs`, `total`, `limit`, `page`, etc.
  - Added population of `loanTypeId` field in responses
- **Branch Controller**:
  - Fixed model import to use `models/branch.model.js` consistently
  - Updated filtering from `isDeleted: false` to `isActive: true`
  - Enhanced role-based access control logic
  - Standardized pagination response format
- **Product Router**:
  - Added `/get` route as alternative to `/` for frontend compatibility
  - Enhanced Swagger documentation with detailed request/response schemas
- **Branch Router**:
  - Updated Swagger documentation to reflect new pagination response format
  - Added query parameter documentation for pagination

### 🛠️ Technical Improvements
- **Model Consistency**: Ensured single source of truth for Branch model across codebase
- **Error Handling**: Improved error messages and HTTP status codes
- **Code Organization**: Better separation of concerns between utilities and controllers
- **Documentation**: Comprehensive API documentation and Swagger schemas

### 📋 API Changes

#### Product APIs
- **GET /product/**: Returns standardized pagination format
- **GET /product/get**: New alternative route for frontend compatibility
- **GET /product/loan-type/:loanTypeId**: Enhanced with proper response format and population

#### Branch APIs  
- **GET /branch/get**: Updated to return standardized pagination format with role-based filtering

### 🔍 Response Format Changes

#### Before:
```json
{
  "success": true,
  "data": {
    "data": [...],
    "currentPage": 1,
    "totalPages": 1,
    "totalItems": 5
  }
}
```

#### After:
```json
{
  "docs": [...],
  "total": 5,
  "limit": 10,
  "page": 1,
  "totalPages": 1,
  "hasNextPage": false,
  "hasPreviousPage": false,
  "nextPage": null,
  "prevPage": null
}
```

### 🐛 Bug Fixes
1. **Server Startup Error**: Fixed Express route handler undefined error preventing server startup
2. **Model Compilation Error**: Resolved Mongoose model overwrite error
3. **Empty API Responses**: Fixed pagination returning empty results due to incorrect filtering
4. **Import Errors**: Resolved missing function imports causing runtime errors

### 📚 Documentation Updates
- Created comprehensive API documentation (`docs/API_DOCUMENTATION.md`)
- Added Swagger schema definitions (`docs/swagger-schemas.js`)
- Updated inline Swagger documentation in router files
- Added detailed changelog with technical explanations

### 🧪 Testing
- Verified all endpoints return correct response formats
- Tested pagination with various page sizes and numbers
- Confirmed role-based access control works correctly
- Validated error handling and status codes

### 🔒 Security
- Maintained existing authentication and authorization mechanisms
- Enhanced role-based access control for branch endpoints
- No security vulnerabilities introduced

### ⚡ Performance
- Optimized database queries with proper filtering
- Maintained efficient pagination implementation
- Added population for related data without performance impact

### 🚨 Breaking Changes
- **Response Format**: Pagination response format has changed (see above)
- **Branch Filtering**: Changed from `isDeleted` to `isActive` filtering
- **Model Usage**: Consolidated to single Branch model definition

### 🔄 Migration Guide
If you're upgrading from a previous version:

1. **Frontend Changes**: Update frontend code to handle new pagination response format
2. **API Calls**: Both `/product/` and `/product/get` work, but `/product/get` is recommended for consistency
3. **Branch Data**: Ensure branch records have `isActive` field instead of relying on `isDeleted`

### 📝 Notes
- Server must be restarted after these changes
- Database migration may be required for branch records
- Frontend applications should be updated to handle new response formats
- All existing authentication and authorization mechanisms remain unchanged

---

## Previous Versions

### [1.1.0] - Previous Release
- Basic product and branch CRUD operations
- Initial pagination implementation
- JWT authentication
- Role-based access control

### [1.0.0] - Initial Release
- Core loan application functionality
- User management
- Basic API endpoints
