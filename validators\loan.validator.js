const Joi = require('joi');
const { validate } = require('../middleware/validation.middleware');
const { objectId } = require('./index');

// Common validation patterns
const commonPatterns = {
  phone: Joi.string().pattern(/^[0-9]{10}$/).required(),
  email: Joi.string().email().required(),
  pincode: Joi.string().pattern(/^[0-9]{6}$/),
  pan: Joi.string().pattern(/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/),
  aadhaar: Joi.string().pattern(/^[0-9]{12}$/),
  amount: Joi.number().min(0).precision(2),
  percentage: Joi.number().min(0).max(100).precision(2)
};

// Address schema
const addressSchema = Joi.object({
  line1: Joi.string().required(),
  line2: Joi.string().allow(''),
  city: Joi.string().required(),
  state: Joi.string().required(),
  pincode: commonPatterns.pincode.required(),
  country: Joi.string().default('India')
});

// Personal details schema
const personalDetailsSchema = Joi.object({
  firstName: Joi.string().min(2).max(50).required(),
  middleName: Joi.string().allow(''),
  lastName: Joi.string().min(1).max(50).required(),
  gender: Joi.string().valid('male', 'female', 'other').required(),
  dateOfBirth: Joi.date().max('now').required(),
  maritalStatus: Joi.string().valid('single', 'married', 'divorced', 'widowed').required(),
  customerNumber: Joi.string().required(),
  ckycrNumber: Joi.string().allow(''),
  pan: commonPatterns.pan.required(),
  aadhaar: commonPatterns.aadhaar.required(),
  phone: commonPatterns.phone,
  email: commonPatterns.email,
  address: addressSchema.required()
});

// Employment details schema
const employmentDetailsSchema = Joi.object({
  employmentType: Joi.string().valid('salaried', 'self_employed', 'professional', 'other').required(),
  companyName: Joi.when('employmentType', {
    is: Joi.valid('salaried', 'professional'),
    then: Joi.string().required(),
    otherwise: Joi.string().allow('')
  }),
  designation: Joi.string().allow(''),
  monthlyIncome: commonPatterns.amount.required(),
  businessName: Joi.when('employmentType', {
    is: 'self_employed',
    then: Joi.string().required(),
    otherwise: Joi.string().allow('')
  }),
  businessType: Joi.when('employmentType', {
    is: 'self_employed',
    then: Joi.string().required(),
    otherwise: Joi.string().allow('')
  }),
  businessAddress: Joi.when('employmentType', {
    is: 'self_employed',
    then: addressSchema.required(),
    otherwise: addressSchema.optional()
  })
});

// Loan details schema
const loanDetailsSchema = Joi.object({
  loanPurpose: Joi.string().valid('construction', 'purchase', 'renovation').required(),
  loanAmount: commonPatterns.amount.required(),
  loanTerm: Joi.number().integer().min(12).max(360).required(), // in months
  moratoriumPeriod: Joi.number().integer().min(0).max(36).default(0), // in months
  propertyValue: commonPatterns.amount.required(),
  propertyAddress: addressSchema.required(),
  propertyType: Joi.string().valid('residential', 'commercial', 'plot').required(),
  ownershipType: Joi.string().valid('freehold', 'leasehold', 'power_of_attorney').required()
});

// Document schema
const documentSchema = Joi.object({
  type: Joi.string().required(),
  url: Joi.string().uri().required(),
  name: Joi.string().required(),
  uploadedAt: Joi.date().default(Date.now)
});

// Main loan application schema
const housingLoanSchema = Joi.object({
  // Application Info
  applicationInfo: Joi.object({
    applicationNumber: Joi.string().required(),
    applicationDate: Joi.date().required(),
    riskCategory: Joi.string().valid('high', 'medium', 'low').required()
  }).required(),

  // Primary Applicant
  primaryApplicant: Joi.object({
    personalDetails: personalDetailsSchema.required(),
    employmentDetails: employmentDetailsSchema.required(),
    existingLoans: Joi.array().items(
      Joi.object({
        bank: Joi.string().required(),
        loanType: Joi.string().required(),
        limit: commonPatterns.amount.required(),
        outstanding: commonPatterns.amount.required(),
        emi: commonPatterns.amount.required(),
        collateral: Joi.string().allow('')
      })
    ).default([])
  }).required(),

  // Co-Applicants
  coApplicants: Joi.array().items(
    Joi.object({
      relationship: Joi.string().required(),
      personalDetails: personalDetailsSchema.required(),
      employmentDetails: employmentDetailsSchema.required()
    })
  ).default([]),

  // Guarantors
  guarantors: Joi.array().items(
    Joi.object({
      fullName: Joi.string().required(),
      relationship: Joi.string().required(),
      phone: commonPatterns.phone,
      address: addressSchema.required(),
      income: commonPatterns.amount.required(),
      netWorth: commonPatterns.amount.required()
    })
  ).default([]),

  // Loan Details
  loanDetails: loanDetailsSchema.required(),

  // Property Details
  propertyDetails: Joi.object({
    surveyNumber: Joi.string().allow(''),
    plotNumber: Joi.string().allow(''),
    area: Joi.number().min(0).required(), // in sq.ft
    builtUpArea: Joi.number().min(0).required(), // in sq.ft
    constructionCost: commonPatterns.amount.when('loanPurpose', {
      is: 'construction',
      then: Joi.required(),
      otherwise: Joi.optional()
    }),
    purchasePrice: commonPatterns.amount.when('loanPurpose', {
      is: 'purchase',
      then: Joi.required(),
      otherwise: Joi.optional()
    }),
    renovationCost: commonPatterns.amount.when('loanPurpose', {
      is: 'renovation',
      then: Joi.required(),
      otherwise: Joi.optional()
    }),
    ownershipType: Joi.string().valid('freehold', 'leasehold', 'power_of_attorney').required(),
    titleClearance: Joi.boolean().required(),
    approvedPlan: Joi.boolean().when('loanPurpose', {
      is: 'construction',
      then: Joi.required(),
      otherwise: Joi.optional()
    })
  }).required(),

  // Documents
  documents: Joi.array().items(documentSchema).min(1).required(),

  // Declaration
  declaration: Joi.object({
    agreedToTerms: Joi.boolean().valid(true).required(),
    informationIsCorrect: Joi.boolean().valid(true).required(),
    noLegalProceedings: Joi.boolean().valid(true).required(),
    propertyUsage: Joi.boolean().valid(true).required()
  }).required(),

  // Bank Use Only
  bankUse: Joi.object({
    inspectionDetails: Joi.object({
      inspectingOfficer: Joi.string().allow(''),
      inspectionDate: Joi.date().allow(null),
      remarks: Joi.string().allow('')
    }),
    eligibleAmount: commonPatterns.amount,
    approvedAmount: commonPatterns.amount,
    interestRate: commonPatterns.percentage,
    finalTerm: Joi.number().integer().min(1),
    margin: commonPatterns.percentage,
    securityDetails: Joi.string().allow(''),
    documentsRequired: Joi.array().items(Joi.string()),
    approvalRecommendation: Joi.string().allow(''),
    approvingOfficer: Joi.string().allow(''),
    approvalDate: Joi.date().allow(null)
  }),

  // Metadata
  status: Joi.string().valid('draft', 'submitted', 'in_review', 'approved', 'rejected', 'disbursed').default('draft'),
  createdBy: objectId.required(),
  updatedBy: objectId.required(),
  branch: objectId.required()
});

// Middleware for validating housing loan data
const validateHousingLoan = (req, res, next) => {
  return validate(housingLoanSchema, 'body')(req, res, next);
};

// Middleware for validating loan update
const validateLoanUpdate = (req, res, next) => {
  const updateSchema = housingLoanSchema.fork(
    ['applicationInfo', 'primaryApplicant', 'coApplicants', 'guarantors', 'loanDetails', 'propertyDetails'],
    (schema) => schema.optional()
  );
  
  return validate(updateSchema, 'body')(req, res, next);
};

module.exports = {
  validateHousingLoan,
  validateLoanUpdate,
  housingLoanSchema
};
