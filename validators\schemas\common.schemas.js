const Joi = require('joi');

// Common patterns used across all loan types
const commonPatterns = {
  phone: Joi.string().pattern(/^[0-9]{10}$/).required()
    .messages({
      'string.pattern.base': 'Phone number must be 10 digits',
      'any.required': 'Phone number is required'
    }),
    
  email: Joi.string().email().required()
    .messages({
      'string.email': 'Please provide a valid email address',
      'any.required': 'Email is required'
    }),
    
  pincode: Joi.string().pattern(/^[0-9]{6}$/)
    .message('Pincode must be 6 digits'),
    
  pan: Joi.string().pattern(/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/)
    .message('Invalid PAN format'),
    
  aadhaar: Joi.string().pattern(/^[0-9]{12}$/)
    .message('Aadhaar must be 12 digits'),
    
  amount: Joi.number().min(0).precision(2)
    .message('Amount must be a positive number with up to 2 decimal places'),
    
  percentage: Joi.number().min(0).max(100).precision(2)
    .message('Percentage must be between 0 and 100 with up to 2 decimal places')
};

// Common address schema
const addressSchema = Joi.object({
  line1: Joi.string().required()
    .messages({ 'any.required': 'Address line 1 is required' }),
  line2: Joi.string().allow(''),
  city: Joi.string().required()
    .messages({ 'any.required': 'City is required' }),
  state: Joi.string().required()
    .messages({ 'any.required': 'State is required' }),
  pincode: commonPatterns.pincode.required()
    .messages({ 'any.required': 'Pincode is required' }),
  country: Joi.string().default('India')
});

// Common personal details schema
const personalDetailsSchema = Joi.object({
  firstName: Joi.string().min(2).max(50).required()
    .messages({
      'string.min': 'First name must be at least 2 characters',
      'string.max': 'First name cannot exceed 50 characters',
      'any.required': 'First name is required'
    }),
  middleName: Joi.string().allow(''),
  lastName: Joi.string().min(1).max(50).required()
    .messages({
      'string.min': 'Last name is required',
      'string.max': 'Last name cannot exceed 50 characters',
      'any.required': 'Last name is required'
    }),
  gender: Joi.string().valid('male', 'female', 'other').required()
    .messages({ 'any.only': 'Please select a valid gender' }),
  dateOfBirth: Joi.date().max('now').required()
    .messages({
      'date.max': 'Date of birth cannot be in the future',
      'any.required': 'Date of birth is required'
    }),
  maritalStatus: Joi.string().valid('single', 'married', 'divorced', 'widowed').required()
    .messages({ 'any.only': 'Please select a valid marital status' }),
  pan: commonPatterns.pan.required(),
  aadhaar: commonPatterns.aadhaar.required(),
  phone: commonPatterns.phone,
  email: commonPatterns.email,
  address: addressSchema.required()
});

// Common employment details schema
const employmentDetailsSchema = Joi.object({
  employmentType: Joi.string()
    .valid('salaried', 'self_employed', 'professional', 'other')
    .required()
    .messages({ 'any.only': 'Please select a valid employment type' }),
  companyName: Joi.when('employmentType', {
    is: Joi.valid('salaried', 'professional'),
    then: Joi.string().required()
      .messages({ 'any.required': 'Company name is required' }),
    otherwise: Joi.string().allow('')
  }),
  designation: Joi.string().allow(''),
  monthlyIncome: commonPatterns.amount.required()
    .messages({ 'any.required': 'Monthly income is required' }),
  businessName: Joi.when('employmentType', {
    is: 'self_employed',
    then: Joi.string().required()
      .messages({ 'any.required': 'Business name is required' }),
    otherwise: Joi.string().allow('')
  }),
  businessType: Joi.when('employmentType', {
    is: 'self_employed',
    then: Joi.string().required()
      .messages({ 'any.required': 'Business type is required' }),
    otherwise: Joi.string().allow('')
  }),
  businessAddress: Joi.when('employmentType', {
    is: 'self_employed',
    then: addressSchema.required()
      .messages({ 'any.required': 'Business address is required' }),
    otherwise: addressSchema.optional()
  })
});

// Common document schema
const documentSchema = Joi.object({
  type: Joi.string().required()
    .messages({ 'any.required': 'Document type is required' }),
  url: Joi.string().uri().required()
    .messages({
      'string.uri': 'Document URL must be a valid URI',
      'any.required': 'Document URL is required'
    }),
  name: Joi.string().required()
    .messages({ 'any.required': 'Document name is required' }),
  uploadedAt: Joi.date().default(() => new Date(), 'Current date')
});

module.exports = {
  commonPatterns,
  addressSchema,
  personalDetailsSchema,
  employmentDetailsSchema,
  documentSchema
};
